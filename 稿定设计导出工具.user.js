// ==UserScript==
// @name         稿定设计导出工具
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  为稿定设计添加PNG和PSD导出功能，支持分层PSD文件
// <AUTHOR>
// @match        https://www.gaoding.com/editor/*
// @match        https://gaoding.com/editor/*
// @grant        none
// @require      https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
// @require      https://unpkg.com/ag-psd@14.3.11/dist/bundle.js
// ==/UserScript==

(function() {
    'use strict';

    // 真正的PSD文件生成器
    class PSDGenerator {
        constructor() {
            this.layers = [];
            this.width = 0;
            this.height = 0;
        }

        addLayer(name, canvas, x, y, width, height, blendMode = 'normal', opacity = 1) {
            this.layers.push({
                name: name,
                canvas: canvas,
                x: x,
                y: y,
                width: width,
                height: height,
                blendMode: blendMode,
                opacity: Math.round(opacity * 255) // PSD使用0-255的透明度
            });
        }

        setCanvasSize(width, height) {
            this.width = width;
            this.height = height;
        }

        // 生成真正的PSD文件
        async generatePSD() {
            if (!window.agPsd) {
                throw new Error('PSD库未加载，请刷新页面重试');
            }

            // 创建PSD文档结构
            const psd = {
                width: this.width,
                height: this.height,
                channels: 3, // RGB
                bitsPerChannel: 8,
                colorMode: 3, // RGB模式
                children: []
            };

            // 添加所有图层
            for (let i = 0; i < this.layers.length; i++) {
                const layer = this.layers[i];

                // 确保canvas是有效的
                if (!layer.canvas || typeof layer.canvas.getContext !== 'function') {
                    console.warn('跳过无效图层:', layer.name);
                    continue;
                }

                psd.children.push({
                    name: layer.name,
                    opacity: layer.opacity,
                    blendMode: layer.blendMode,
                    canvas: layer.canvas, // 直接使用canvas对象
                    left: layer.x,
                    top: layer.y,
                    right: layer.x + layer.width,
                    bottom: layer.y + layer.height
                });
            }

            // 如果没有有效图层，至少添加背景
            if (psd.children.length === 0) {
                console.warn('没有有效图层，创建空白背景');
                const emptyCanvas = document.createElement('canvas');
                emptyCanvas.width = this.width;
                emptyCanvas.height = this.height;
                const ctx = emptyCanvas.getContext('2d');
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, this.width, this.height);

                psd.children.push({
                    name: '背景',
                    opacity: 255,
                    blendMode: 'normal',
                    canvas: emptyCanvas,
                    left: 0,
                    top: 0,
                    right: this.width,
                    bottom: this.height
                });
            }

            try {
                console.log('PSD结构:', psd);
                // 使用ag-psd库生成PSD文件
                const buffer = window.agPsd.writePsd(psd);
                return new Blob([buffer], { type: 'application/octet-stream' });
            } catch (error) {
                console.error('PSD生成错误:', error);
                throw new Error('PSD文件生成失败: ' + error.message);
            }
        }
    }

    // 等待页面加载完成
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                } else {
                    setTimeout(check, 100);
                }
            }
            
            check();
        });
    }

    // 获取画布数据
    function getCanvasData() {
        const canvases = document.querySelectorAll('canvas');
        let mainCanvas = null;

        // 优先查找稿定设计的主画布
        for (let canvas of canvases) {
            // 检查是否是主编辑画布
            if (canvas.className && (
                canvas.className.includes('main') ||
                canvas.className.includes('editor') ||
                canvas.className.includes('design')
            )) {
                mainCanvas = canvas;
                break;
            }
        }

        // 如果没找到特定的画布，寻找最大的那个
        if (!mainCanvas) {
            let maxArea = 0;
            canvases.forEach(canvas => {
                const area = canvas.width * canvas.height;
                if (area > maxArea && area > 10000) { // 过滤掉太小的画布
                    maxArea = area;
                    mainCanvas = canvas;
                }
            });
        }

        if (!mainCanvas) {
            throw new Error('未找到合适的画布元素');
        }

        console.log('使用画布:', mainCanvas, '尺寸:', mainCanvas.width, 'x', mainCanvas.height);

        return {
            canvas: mainCanvas,
            width: mainCanvas.width,
            height: mainCanvas.height,
            context: mainCanvas.getContext('2d')
        };
    }

    // 获取设计元素信息
    function getDesignElements() {
        const elements = [];

        // 方法1: 尝试从fabric.js获取对象
        if (window.fabric && window.fabric.Canvas) {
            const fabricCanvases = document.querySelectorAll('canvas');
            fabricCanvases.forEach(canvas => {
                if (canvas.__fabric) {
                    const fabricCanvas = canvas.__fabric;
                    fabricCanvas.getObjects().forEach((obj, index) => {
                        elements.push({
                            type: obj.type,
                            name: obj.name || `${obj.type}_${index + 1}`,
                            left: obj.left,
                            top: obj.top,
                            width: obj.width * (obj.scaleX || 1),
                            height: obj.height * (obj.scaleY || 1),
                            angle: obj.angle || 0,
                            opacity: obj.opacity || 1,
                            object: obj
                        });
                    });
                }
            });
        }

        // 方法2: 尝试从稿定设计的特定结构获取
        if (elements.length === 0) {
            // 查找稿定设计可能的元素选择器
            const selectors = [
                '[class*="gd-element"]',
                '[class*="element"]',
                '[class*="layer"]',
                '[class*="object"]',
                '[class*="component"]',
                '[data-element-id]',
                '[data-layer-id]'
            ];

            selectors.forEach(selector => {
                const foundElements = document.querySelectorAll(selector);
                foundElements.forEach((el, index) => {
                    const rect = el.getBoundingClientRect();
                    const canvasRect = document.querySelector('canvas')?.getBoundingClientRect();

                    if (rect.width > 0 && rect.height > 0 && canvasRect) {
                        elements.push({
                            type: 'dom-element',
                            name: `元素_${selector.replace(/[\[\].*"']/g, '')}_${index + 1}`,
                            left: rect.left - canvasRect.left,
                            top: rect.top - canvasRect.top,
                            width: rect.width,
                            height: rect.height,
                            element: el
                        });
                    }
                });
            });
        }

        // 方法3: 尝试从全局变量获取（稿定设计可能的API）
        if (elements.length === 0 && window.gdEditor) {
            try {
                const editorData = window.gdEditor.getData ? window.gdEditor.getData() : null;
                if (editorData && editorData.elements) {
                    editorData.elements.forEach((el, index) => {
                        elements.push({
                            type: el.type || 'unknown',
                            name: el.name || `编辑器元素${index + 1}`,
                            left: el.x || el.left || 0,
                            top: el.y || el.top || 0,
                            width: el.width || 100,
                            height: el.height || 100,
                            opacity: el.opacity || 1,
                            editorData: el
                        });
                    });
                }
            } catch (e) {
                console.warn('无法从编辑器获取数据:', e);
            }
        }

        console.log('找到的设计元素:', elements);
        return elements;
    }

    // 导出PNG
    async function exportPNG() {
        try {
            const canvasData = getCanvasData();
            const link = document.createElement('a');
            link.download = `稿定设计_${Date.now()}.png`;
            link.href = canvasData.canvas.toDataURL('image/png', 1.0);
            link.click();
            
            showMessage('PNG导出成功！', 'success');
        } catch (error) {
            console.error('PNG导出失败:', error);
            showMessage('PNG导出失败: ' + error.message, 'error');
        }
    }

    // 导出真正的PSD文件
    async function exportPSD() {
        try {
            showMessage('正在生成PSD文件，请稍候...', 'info');

            const canvasData = getCanvasData();
            const elements = getDesignElements();
            const psdGen = new PSDGenerator();

            psdGen.setCanvasSize(canvasData.width, canvasData.height);

            // 添加背景图层（完整画布）
            const backgroundCanvas = document.createElement('canvas');
            backgroundCanvas.width = canvasData.width;
            backgroundCanvas.height = canvasData.height;
            const bgCtx = backgroundCanvas.getContext('2d');
            bgCtx.drawImage(canvasData.canvas, 0, 0);
            psdGen.addLayer('背景', backgroundCanvas, 0, 0, canvasData.width, canvasData.height, 'normal', 1);

            // 尝试获取各个图层
            if (elements.length > 0) {
                console.log('找到设计元素:', elements.length, '个');

                for (let i = 0; i < elements.length; i++) {
                    const element = elements[i];

                    try {
                        // 创建临时画布来渲染单个元素
                        const tempCanvas = document.createElement('canvas');
                        tempCanvas.width = Math.max(Math.round(element.width), 1);
                        tempCanvas.height = Math.max(Math.round(element.height), 1);
                        const tempCtx = tempCanvas.getContext('2d');

                        // 设置透明背景
                        tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);

                        if (element.object) {
                            // 如果是fabric对象
                            if (element.object.toCanvasElement) {
                                const objCanvas = element.object.toCanvasElement();
                                tempCtx.drawImage(objCanvas, 0, 0);
                            } else if (element.object._element && element.object.type === 'image') {
                                // 图片对象
                                tempCtx.drawImage(element.object._element, 0, 0, tempCanvas.width, tempCanvas.height);
                            } else if (element.object.type === 'text' || element.object.type === 'textbox') {
                                // 文字对象
                                tempCtx.font = `${element.object.fontSize || 16}px ${element.object.fontFamily || 'Arial'}`;
                                tempCtx.fillStyle = element.object.fill || '#000000';
                                tempCtx.textAlign = element.object.textAlign || 'left';
                                tempCtx.fillText(element.object.text || '', 0, element.object.fontSize || 16);
                            } else {
                                // 其他fabric对象，尝试渲染
                                const fabricCanvas = new fabric.StaticCanvas(null, {
                                    width: tempCanvas.width,
                                    height: tempCanvas.height
                                });
                                fabricCanvas.add(element.object.clone());
                                fabricCanvas.renderAll();
                                tempCtx.drawImage(fabricCanvas.getElement(), 0, 0);
                                fabricCanvas.dispose();
                            }
                        } else if (element.element) {
                            // 如果是DOM元素，使用html2canvas
                            const elementCanvas = await html2canvas(element.element, {
                                width: tempCanvas.width,
                                height: tempCanvas.height,
                                scale: 1,
                                backgroundColor: null,
                                useCORS: true
                            });
                            tempCtx.drawImage(elementCanvas, 0, 0);
                        }

                        // 添加到PSD生成器
                        psdGen.addLayer(
                            element.name || `图层${i + 1}`,
                            tempCanvas,
                            Math.round(element.left || 0),
                            Math.round(element.top || 0),
                            tempCanvas.width,
                            tempCanvas.height,
                            'normal',
                            element.opacity || 1
                        );

                        console.log(`已处理图层: ${element.name}, 尺寸: ${tempCanvas.width}x${tempCanvas.height}`);
                    } catch (e) {
                        console.warn('无法处理图层:', element.name, e);
                    }
                }
            } else {
                // 如果没有找到分层信息，尝试其他方法
                console.log('未找到fabric对象，尝试其他方法提取图层');
                await tryAlternativeLayerExtraction(psdGen);
            }

            // 生成PSD文件
            console.log('开始生成PSD文件...');
            const psdBlob = await psdGen.generatePSD();

            const link = document.createElement('a');
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            link.download = `稿定设计_${timestamp}.psd`;
            link.href = URL.createObjectURL(psdBlob);
            link.click();

            showMessage('✅ PSD文件导出成功！可在Photoshop中打开编辑', 'success');
        } catch (error) {
            console.error('PSD文件导出失败:', error);
            showMessage('❌ PSD导出失败: ' + error.message, 'error');
        }
    }

    // 尝试其他方法提取图层
    async function tryAlternativeLayerExtraction(psdGen) {
        let layerCount = 0;

        // 方法1: 查找所有图片元素
        const images = document.querySelectorAll('img');
        console.log('找到图片元素:', images.length, '个');

        for (let i = 0; i < Math.min(images.length, 20); i++) {
            const img = images[i];
            if (img.src && img.offsetWidth > 20 && img.offsetHeight > 20) {
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = img.naturalWidth || img.offsetWidth;
                tempCanvas.height = img.naturalHeight || img.offsetHeight;
                const tempCtx = tempCanvas.getContext('2d');

                try {
                    // 等待图片加载
                    if (!img.complete) {
                        await new Promise((resolve) => {
                            img.onload = resolve;
                            img.onerror = resolve;
                        });
                    }

                    tempCtx.drawImage(img, 0, 0, tempCanvas.width, tempCanvas.height);
                    psdGen.addLayer(
                        `图片_${layerCount + 1}`,
                        tempCanvas,
                        img.offsetLeft,
                        img.offsetTop,
                        tempCanvas.width,
                        tempCanvas.height,
                        'normal',
                        1
                    );
                    layerCount++;
                    console.log(`已添加图片图层: ${tempCanvas.width}x${tempCanvas.height}`);
                } catch (e) {
                    console.warn('无法处理图片:', e);
                }
            }
        }

        // 方法2: 查找文字元素
        const textSelectors = [
            '[class*="text"]',
            '[data-text]',
            'p', 'span', 'div',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
        ];

        for (let selector of textSelectors) {
            const textElements = document.querySelectorAll(selector);
            console.log(`找到文字元素 (${selector}):`, textElements.length, '个');

            for (let i = 0; i < Math.min(textElements.length, 10); i++) {
                const textEl = textElements[i];
                if (textEl.textContent.trim() &&
                    textEl.offsetWidth > 10 &&
                    textEl.offsetHeight > 10 &&
                    layerCount < 50) { // 限制总图层数

                    try {
                        const textCanvas = await html2canvas(textEl, {
                            backgroundColor: null,
                            scale: 1,
                            useCORS: true,
                            allowTaint: true
                        });

                        if (textCanvas.width > 0 && textCanvas.height > 0) {
                            psdGen.addLayer(
                                `文字_${layerCount + 1}`,
                                textCanvas,
                                textEl.offsetLeft,
                                textEl.offsetTop,
                                textCanvas.width,
                                textCanvas.height,
                                'normal',
                                1
                            );
                            layerCount++;
                            console.log(`已添加文字图层: ${textCanvas.width}x${textCanvas.height}`);
                        }
                    } catch (e) {
                        console.warn('无法处理文字元素:', e);
                    }
                }
            }
        }

        console.log(`总共提取了 ${layerCount} 个图层`);
    }

    // 显示消息
    function showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
            ${type === 'success' ? 'background-color: #52c41a;' : 
              type === 'error' ? 'background-color: #ff4d4f;' : 
              'background-color: #1890ff;'}
        `;
        messageDiv.textContent = message;
        document.body.appendChild(messageDiv);

        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }

    // 创建导出按钮
    function createExportButtons() {
        // 检查是否已经存在按钮
        if (document.getElementById('gd-export-buttons')) {
            return;
        }

        const buttonContainer = document.createElement('div');
        buttonContainer.id = 'gd-export-buttons';
        buttonContainer.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 99999;
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        `;

        // 标题
        const title = document.createElement('div');
        title.textContent = '导出工具';
        title.style.cssText = `
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
            text-align: center;
            font-weight: 500;
        `;

        const pngButton = document.createElement('button');
        pngButton.textContent = '📷 导出PNG';
        pngButton.style.cssText = `
            padding: 8px 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            min-width: 120px;
        `;
        pngButton.addEventListener('click', exportPNG);
        pngButton.addEventListener('mouseenter', () => {
            pngButton.style.transform = 'translateY(-1px)';
            pngButton.style.boxShadow = '0 4px 8px rgba(102, 126, 234, 0.3)';
        });
        pngButton.addEventListener('mouseleave', () => {
            pngButton.style.transform = 'translateY(0)';
            pngButton.style.boxShadow = 'none';
        });

        const psdButton = document.createElement('button');
        psdButton.textContent = '🎨 导出PSD';
        psdButton.style.cssText = `
            padding: 8px 16px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            min-width: 120px;
        `;
        psdButton.addEventListener('click', exportPSD);
        psdButton.addEventListener('mouseenter', () => {
            psdButton.style.transform = 'translateY(-1px)';
            psdButton.style.boxShadow = '0 4px 8px rgba(245, 87, 108, 0.3)';
        });
        psdButton.addEventListener('mouseleave', () => {
            psdButton.style.transform = 'translateY(0)';
            psdButton.style.boxShadow = 'none';
        });

        // 信息按钮
        const infoButton = document.createElement('button');
        infoButton.textContent = 'ℹ️ 说明';
        infoButton.style.cssText = `
            padding: 6px 16px;
            background: #f0f0f0;
            color: #666;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            min-width: 120px;
        `;
        infoButton.addEventListener('click', () => {
            showMessage(`导出说明：
• PNG：导出完整的设计图片
• PSD：导出真正的PSD文件，支持分层
• PSD文件可直接在Photoshop中打开编辑
• 保持所见即所得的效果和图层结构`, 'info');
        });

        buttonContainer.appendChild(title);
        buttonContainer.appendChild(pngButton);
        buttonContainer.appendChild(psdButton);
        buttonContainer.appendChild(infoButton);
        document.body.appendChild(buttonContainer);

        // 添加拖拽功能
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        title.addEventListener('mousedown', (e) => {
            isDragging = true;
            dragOffset.x = e.clientX - buttonContainer.offsetLeft;
            dragOffset.y = e.clientY - buttonContainer.offsetTop;
            title.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                buttonContainer.style.left = (e.clientX - dragOffset.x) + 'px';
                buttonContainer.style.top = (e.clientY - dragOffset.y) + 'px';
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            title.style.cursor = 'grab';
        });

        title.style.cursor = 'grab';
    }

    // 初始化
    async function init() {
        try {
            console.log('稿定设计导出工具开始初始化...');

            // 等待页面加载
            await waitForElement('canvas', 20000);
            console.log('找到画布元素');

            // 等待一段时间确保所有资源加载完成
            setTimeout(() => {
                try {
                    createExportButtons();
                    showMessage('🎉 稿定设计导出工具已就绪！', 'success');
                    console.log('导出工具初始化完成');
                } catch (e) {
                    console.error('创建按钮失败:', e);
                    showMessage('按钮创建失败: ' + e.message, 'error');
                }
            }, 3000);

        } catch (error) {
            console.error('初始化失败:', error);
            showMessage('导出工具初始化失败，请刷新页面重试', 'error');

            // 如果初始化失败，尝试延迟重试
            setTimeout(() => {
                console.log('尝试重新初始化...');
                init();
            }, 5000);
        }
    }

    // 监听页面变化，重新初始化
    function observePageChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // 检查是否有新的画布元素
                    const hasCanvas = document.querySelector('canvas');
                    const hasButtons = document.getElementById('gd-export-buttons');

                    if (hasCanvas && !hasButtons) {
                        console.log('检测到页面变化，重新初始化按钮');
                        setTimeout(createExportButtons, 1000);
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            init();
            observePageChanges();
        });
    } else {
        init();
        observePageChanges();
    }

    console.log('稿定设计导出工具脚本已加载');

})();
