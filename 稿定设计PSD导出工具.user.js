// ==UserScript==
// @name         稿定设计PSD导出工具
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  为稿定设计添加真正的PSD文件导出功能，支持分层编辑
// <AUTHOR>
// @match        https://www.gaoding.com/editor/*
// @match        https://gaoding.com/editor/*
// @grant        none
// @require      https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
// @require      https://unpkg.com/ag-psd@14.3.11/dist/bundle.js
// ==/UserScript==

(function() {
    'use strict';

    let isInitialized = false;

    // 等待元素出现
    function waitForElement(selector, timeout = 15000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`未找到元素: ${selector}`));
                } else {
                    setTimeout(check, 200);
                }
            }
            check();
        });
    }

    // 显示消息提示
    function showMessage(message, type = 'info', duration = 4000) {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-size: 14px;
            z-index: 100000;
            max-width: 350px;
            word-wrap: break-word;
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            ${type === 'success' ? 'background: linear-gradient(135deg, #52c41a, #73d13d);' : 
              type === 'error' ? 'background: linear-gradient(135deg, #ff4d4f, #ff7875);' : 
              'background: linear-gradient(135deg, #1890ff, #40a9ff);'}
        `;
        messageDiv.innerHTML = message.replace(/\n/g, '<br>');
        document.body.appendChild(messageDiv);

        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateX(100%)';
                messageDiv.style.transition = 'all 0.3s ease';
                setTimeout(() => messageDiv.remove(), 300);
            }
        }, duration);
    }

    // 获取最佳画布
    function getBestCanvas() {
        const canvases = Array.from(document.querySelectorAll('canvas'));
        
        // 过滤掉太小的画布
        const validCanvases = canvases.filter(canvas => 
            canvas.width > 100 && canvas.height > 100
        );

        if (validCanvases.length === 0) {
            throw new Error('未找到有效的画布');
        }

        // 选择最大的画布
        const bestCanvas = validCanvases.reduce((largest, current) => {
            const largestArea = largest.width * largest.height;
            const currentArea = current.width * current.height;
            return currentArea > largestArea ? current : largest;
        });

        console.log('选择的画布尺寸:', bestCanvas.width, 'x', bestCanvas.height);
        return bestCanvas;
    }

    // 真正的PSD生成器
    class PSDGenerator {
        constructor() {
            this.layers = [];
            this.width = 0;
            this.height = 0;
        }

        setCanvasSize(width, height) {
            this.width = width;
            this.height = height;
        }

        addLayer(name, canvas, x = 0, y = 0, opacity = 1) {
            // 确保画布有内容
            const ctx = canvas.getContext('2d');
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            this.layers.push({
                name: name,
                canvas: canvas,
                imageData: imageData,
                left: Math.round(x),
                top: Math.round(y),
                right: Math.round(x + canvas.width),
                bottom: Math.round(y + canvas.height),
                opacity: Math.round(opacity * 255)
            });
        }

        async generatePSD() {
            if (!window.agPsd) {
                throw new Error('PSD库未加载完成，请刷新页面重试');
            }

            try {
                // 创建PSD文档结构
                const psd = {
                    width: this.width,
                    height: this.height,
                    channels: 3,
                    bitsPerChannel: 8,
                    colorMode: 3, // RGB
                    children: []
                };

                // 添加图层
                for (let i = 0; i < this.layers.length; i++) {
                    const layer = this.layers[i];
                    
                    psd.children.push({
                        name: layer.name,
                        opacity: layer.opacity,
                        blendMode: 'normal',
                        left: layer.left,
                        top: layer.top,
                        right: layer.right,
                        bottom: layer.bottom,
                        canvas: layer.imageData
                    });
                }

                console.log('PSD结构:', psd);
                
                // 生成PSD文件
                const arrayBuffer = window.agPsd.writePsd(psd);
                return new Blob([arrayBuffer], { type: 'application/octet-stream' });
                
            } catch (error) {
                console.error('PSD生成详细错误:', error);
                throw new Error(`PSD生成失败: ${error.message}`);
            }
        }
    }

    // 导出PNG
    async function exportPNG() {
        try {
            showMessage('正在生成PNG图片...', 'info');
            
            const canvas = getBestCanvas();
            
            const link = document.createElement('a');
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            link.download = `稿定设计_${timestamp}.png`;
            link.href = canvas.toDataURL('image/png', 1.0);
            link.click();
            
            showMessage('✅ PNG导出成功！', 'success');
        } catch (error) {
            console.error('PNG导出失败:', error);
            showMessage('❌ PNG导出失败: ' + error.message, 'error');
        }
    }

    // 导出PSD文件
    async function exportPSD() {
        try {
            showMessage('正在分析页面结构...\n这可能需要几秒钟时间', 'info', 6000);
            
            const canvas = getBestCanvas();
            const psdGen = new PSDGenerator();
            psdGen.setCanvasSize(canvas.width, canvas.height);

            // 添加主画布作为背景
            const backgroundCanvas = document.createElement('canvas');
            backgroundCanvas.width = canvas.width;
            backgroundCanvas.height = canvas.height;
            const bgCtx = backgroundCanvas.getContext('2d');
            bgCtx.drawImage(canvas, 0, 0);
            psdGen.addLayer('背景', backgroundCanvas, 0, 0, 1);

            // 尝试提取图层
            let layerCount = 1; // 背景已经是1层了

            // 方法1: 提取图片
            const images = document.querySelectorAll('img');
            console.log('找到图片:', images.length);
            
            for (let i = 0; i < Math.min(images.length, 15); i++) {
                const img = images[i];
                if (img.src && img.offsetWidth > 30 && img.offsetHeight > 30) {
                    try {
                        const imgCanvas = document.createElement('canvas');
                        imgCanvas.width = img.naturalWidth || img.offsetWidth;
                        imgCanvas.height = img.naturalHeight || img.offsetHeight;
                        const imgCtx = imgCanvas.getContext('2d');
                        
                        if (img.complete) {
                            imgCtx.drawImage(img, 0, 0, imgCanvas.width, imgCanvas.height);
                            psdGen.addLayer(`图片${layerCount}`, imgCanvas, img.offsetLeft, img.offsetTop, 1);
                            layerCount++;
                        }
                    } catch (e) {
                        console.warn('图片处理失败:', e);
                    }
                }
            }

            // 方法2: 提取文本（使用html2canvas）
            const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
            console.log('找到文本元素:', textElements.length);
            
            for (let i = 0; i < Math.min(textElements.length, 10); i++) {
                const textEl = textElements[i];
                if (textEl.textContent.trim() && 
                    textEl.offsetWidth > 20 && 
                    textEl.offsetHeight > 10 &&
                    layerCount < 25) {
                    
                    try {
                        const textCanvas = await html2canvas(textEl, {
                            backgroundColor: null,
                            scale: 1,
                            useCORS: true,
                            allowTaint: true,
                            width: textEl.offsetWidth,
                            height: textEl.offsetHeight
                        });
                        
                        if (textCanvas.width > 0 && textCanvas.height > 0) {
                            psdGen.addLayer(`文本${layerCount}`, textCanvas, textEl.offsetLeft, textEl.offsetTop, 1);
                            layerCount++;
                        }
                    } catch (e) {
                        console.warn('文本处理失败:', e);
                    }
                }
            }

            showMessage('正在生成PSD文件...\n请稍候', 'info', 8000);
            
            // 生成PSD
            const psdBlob = await psdGen.generatePSD();
            
            // 下载文件
            const link = document.createElement('a');
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            link.download = `稿定设计_${timestamp}.psd`;
            link.href = URL.createObjectURL(psdBlob);
            link.click();
            
            showMessage(`✅ PSD文件导出成功！\n包含 ${layerCount} 个图层\n可在Photoshop中打开编辑`, 'success', 5000);
            
        } catch (error) {
            console.error('PSD导出失败:', error);
            showMessage('❌ PSD导出失败:\n' + error.message, 'error', 6000);
        }
    }

    // 创建导出按钮
    function createExportButtons() {
        if (document.getElementById('psd-export-panel')) {
            return;
        }

        const panel = document.createElement('div');
        panel.id = 'psd-export-panel';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 99999;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            min-width: 180px;
        `;

        const title = document.createElement('div');
        title.textContent = '🎨 PSD导出工具';
        title.style.cssText = `
            font-size: 16px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        `;

        const buttonStyle = `
            width: 100%;
            padding: 12px;
            margin-bottom: 10px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        `;

        const pngBtn = document.createElement('button');
        pngBtn.textContent = '📷 导出PNG';
        pngBtn.style.cssText = buttonStyle + `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        `;
        pngBtn.onclick = exportPNG;

        const psdBtn = document.createElement('button');
        psdBtn.textContent = '🎨 导出PSD';
        psdBtn.style.cssText = buttonStyle + `
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        `;
        psdBtn.onclick = exportPSD;

        const infoBtn = document.createElement('button');
        infoBtn.textContent = 'ℹ️ 使用说明';
        infoBtn.style.cssText = buttonStyle + `
            background: #f8f9fa;
            color: #666;
            font-size: 12px;
            padding: 8px;
        `;
        infoBtn.onclick = () => {
            showMessage(`🎨 PSD导出工具说明：\n\n• PNG：导出高质量图片\n• PSD：导出真正的PSD文件\n• PSD支持分层，可在PS中编辑\n• 保持原始尺寸和位置\n• 支持图片和文字图层`, 'info', 8000);
        };

        // 添加悬停效果
        [pngBtn, psdBtn].forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-3px)';
                btn.style.boxShadow = '0 6px 20px rgba(0,0,0,0.25)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'translateY(0)';
                btn.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            });
        });

        panel.appendChild(title);
        panel.appendChild(pngBtn);
        panel.appendChild(psdBtn);
        panel.appendChild(infoBtn);
        document.body.appendChild(panel);
    }

    // 初始化
    async function init() {
        if (isInitialized) return;
        
        try {
            console.log('PSD导出工具初始化中...');
            
            // 等待ag-psd库加载
            let attempts = 0;
            while (!window.agPsd && attempts < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (!window.agPsd) {
                throw new Error('PSD库加载失败');
            }
            
            await waitForElement('canvas');
            
            setTimeout(() => {
                createExportButtons();
                showMessage('🎉 PSD导出工具已就绪！\n支持真正的PSD格式导出', 'success');
                isInitialized = true;
            }, 2000);
            
        } catch (error) {
            console.error('初始化失败:', error);
            showMessage('❌ 初始化失败:\n' + error.message + '\n请刷新页面重试', 'error');
        }
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    console.log('稿定设计PSD导出工具已加载');

})();
