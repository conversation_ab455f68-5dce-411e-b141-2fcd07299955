// ==UserScript==
// @name         稿定设计真正分层导出
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  真正的分层导出，每个素材独立图层
// <AUTHOR>
// @match        https://www.gaoding.com/editor/*
// @match        https://gaoding.com/editor/*
// @grant        GM_xmlhttpRequest
// @require      https://unpkg.com/ag-psd@14.3.11/dist/bundle.js
// ==/UserScript==

(function() {
    'use strict';

    let isProcessing = false;

    // 等待ag-psd库加载
    function waitForAgPsd() {
        return new Promise((resolve) => {
            function check() {
                if (window.agPsd) {
                    console.log('✅ ag-psd库加载完成');
                    resolve();
                } else {
                    setTimeout(check, 500);
                }
            }
            check();
        });
    }

    // 直接从稿定设计获取真实数据
    function getGaodingData() {
        console.log('🔍 直接获取稿定设计数据...');
        
        // 查找所有可能包含数据的对象
        const possibleSources = [
            // React DevTools 数据
            () => {
                const reactRoot = document.querySelector('#root, [data-reactroot]');
                if (reactRoot) {
                    for (const key in reactRoot) {
                        if (key.startsWith('__reactInternalInstance') || key.startsWith('_reactInternalFiber')) {
                            return searchReactTree(reactRoot[key]);
                        }
                    }
                }
                return null;
            },
            
            // 全局变量搜索
            () => {
                const globals = ['__INITIAL_STATE__', '__PRELOADED_STATE__', 'store', 'app'];
                for (const global of globals) {
                    if (window[global]) {
                        const data = searchForDesignData(window[global]);
                        if (data) return data;
                    }
                }
                return null;
            },
            
            // DOM数据属性
            () => {
                const elements = document.querySelectorAll('[data-*]');
                for (const el of elements) {
                    for (const attr of el.attributes) {
                        if (attr.name.startsWith('data-') && attr.value) {
                            try {
                                const data = JSON.parse(attr.value);
                                if (isValidDesignData(data)) return data;
                            } catch (e) {}
                        }
                    }
                }
                return null;
            }
        ];

        for (const source of possibleSources) {
            try {
                const data = source();
                if (data) {
                    console.log('✅ 找到设计数据:', data);
                    return data;
                }
            } catch (e) {
                console.warn('数据源失败:', e);
            }
        }

        console.warn('⚠️ 未找到设计数据，使用DOM分析');
        return analyzeDOMElements();
    }

    // 搜索React组件树
    function searchReactTree(fiber, depth = 0) {
        if (!fiber || depth > 15) return null;

        // 检查props和state
        const candidates = [
            fiber.memoizedProps,
            fiber.memoizedState,
            fiber.pendingProps,
            fiber.stateNode?.props,
            fiber.stateNode?.state
        ].filter(Boolean);

        for (const candidate of candidates) {
            if (isValidDesignData(candidate)) return candidate;
            
            // 深度搜索对象属性
            const found = searchForDesignData(candidate);
            if (found) return found;
        }

        // 递归搜索子节点
        if (fiber.child) {
            const result = searchReactTree(fiber.child, depth + 1);
            if (result) return result;
        }

        if (fiber.sibling) {
            const result = searchReactTree(fiber.sibling, depth + 1);
            if (result) return result;
        }

        return null;
    }

    // 深度搜索设计数据
    function searchForDesignData(obj, depth = 0, visited = new Set()) {
        if (!obj || typeof obj !== 'object' || depth > 5 || visited.has(obj)) {
            return null;
        }
        visited.add(obj);

        if (isValidDesignData(obj)) return obj;

        for (const key in obj) {
            if (key.startsWith('_') || key === 'constructor') continue;
            
            try {
                const value = obj[key];
                if (value && typeof value === 'object') {
                    const result = searchForDesignData(value, depth + 1, visited);
                    if (result) return result;
                }
            } catch (e) {}
        }

        return null;
    }

    // 验证是否为有效的设计数据
    function isValidDesignData(obj) {
        return obj && 
               typeof obj === 'object' && 
               Array.isArray(obj.elements) && 
               obj.elements.length > 0 &&
               obj.elements.some(el => 
                   el.type && 
                   (el.left !== undefined || el.x !== undefined) &&
                   (el.width !== undefined || el.w !== undefined)
               );
    }

    // DOM元素分析备用方案
    function analyzeDOMElements() {
        console.log('🔍 分析DOM元素...');
        
        const canvas = document.querySelector('.infinite-canvas, [class*="canvas"], [class*="editor"]');
        if (!canvas) {
            throw new Error('未找到画布容器');
        }

        const canvasRect = canvas.getBoundingClientRect();
        const elements = [];

        // 查找所有可能的设计元素
        const selectors = [
            'img[src]:not([class*="ui"]):not([class*="icon"])',
            '[class*="text"]:not([class*="ui"]):not(button)',
            'svg:not([class*="ui"]):not([class*="icon"])',
            '[style*="background-image"]:not([class*="ui"])'
        ];

        let index = 0;
        for (const selector of selectors) {
            const domElements = canvas.querySelectorAll(selector);
            
            for (const el of domElements) {
                const rect = el.getBoundingClientRect();
                
                if (rect.width < 5 || rect.height < 5) continue;
                if (!isElementInCanvas(rect, canvasRect)) continue;

                const element = {
                    id: `element_${index++}`,
                    name: getElementName(el),
                    type: getElementType(el),
                    left: Math.round(rect.left - canvasRect.left),
                    top: Math.round(rect.top - canvasRect.top),
                    width: Math.round(rect.width),
                    height: Math.round(rect.height),
                    domElement: el
                };

                // 提取特定属性
                if (element.type === 'image') {
                    element.url = el.src || getBackgroundImageUrl(el);
                } else if (element.type === 'text') {
                    element.content = el.textContent?.trim() || '';
                    const style = getComputedStyle(el);
                    element.fontSize = parseInt(style.fontSize) || 16;
                    element.fontFamily = style.fontFamily || 'Arial';
                    element.color = style.color || '#000000';
                }

                elements.push(element);
            }
        }

        return {
            width: Math.round(canvasRect.width),
            height: Math.round(canvasRect.height),
            elements: elements
        };
    }

    // 辅助函数
    function isElementInCanvas(rect, canvasRect) {
        return rect.left >= canvasRect.left - 50 && 
               rect.top >= canvasRect.top - 50 &&
               rect.right <= canvasRect.right + 50 &&
               rect.bottom <= canvasRect.bottom + 50;
    }

    function getElementName(el) {
        return el.id || el.className.split(' ')[0] || el.tagName.toLowerCase();
    }

    function getElementType(el) {
        if (el.tagName === 'IMG') return 'image';
        if (el.tagName === 'SVG') return 'svg';
        if (el.textContent?.trim()) return 'text';
        if (getBackgroundImageUrl(el)) return 'image';
        return 'shape';
    }

    function getBackgroundImageUrl(el) {
        const style = getComputedStyle(el);
        const bgImage = style.backgroundImage;
        if (bgImage && bgImage !== 'none') {
            const match = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
            return match ? match[1] : null;
        }
        return null;
    }

    // 创建独立图层
    async function createLayerCanvas(element) {
        const canvas = document.createElement('canvas');
        canvas.width = element.width;
        canvas.height = element.height;
        const ctx = canvas.getContext('2d');

        if (element.type === 'image' && element.url) {
            return await loadImageToCanvas(element.url, canvas);
        } else if (element.type === 'text' && element.content) {
            return createTextCanvas(element, canvas);
        } else if (element.domElement) {
            return await captureElementToCanvas(element.domElement, canvas);
        }

        return canvas;
    }

    // 加载图片到画布
    function loadImageToCanvas(url, canvas) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            
            img.onload = () => {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                resolve(canvas);
            };
            
            img.onerror = () => {
                if (typeof GM_xmlhttpRequest !== 'undefined') {
                    GM_xmlhttpRequest({
                        method: 'GET',
                        url: url,
                        responseType: 'blob',
                        headers: { 'Referer': 'https://www.gaoding.com/' },
                        onload: (response) => {
                            const blobUrl = URL.createObjectURL(response.response);
                            const fallbackImg = new Image();
                            fallbackImg.onload = () => {
                                const ctx = canvas.getContext('2d');
                                ctx.clearRect(0, 0, canvas.width, canvas.height);
                                ctx.drawImage(fallbackImg, 0, 0, canvas.width, canvas.height);
                                URL.revokeObjectURL(blobUrl);
                                resolve(canvas);
                            };
                            fallbackImg.onerror = () => {
                                URL.revokeObjectURL(blobUrl);
                                reject(new Error('图片加载失败'));
                            };
                            fallbackImg.src = blobUrl;
                        },
                        onerror: () => reject(new Error('网络请求失败'))
                    });
                } else {
                    reject(new Error('图片加载失败'));
                }
            };
            
            img.src = url;
        });
    }

    // 创建文字画布
    function createTextCanvas(element, canvas) {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        ctx.font = `${element.fontSize}px ${element.fontFamily}`;
        ctx.fillStyle = element.color;
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        
        const lines = element.content.split('\n');
        const lineHeight = element.fontSize * 1.2;
        
        lines.forEach((line, index) => {
            ctx.fillText(line, 0, index * lineHeight);
        });
        
        return canvas;
    }

    // 捕获DOM元素到画布
    async function captureElementToCanvas(element, canvas) {
        // 简单的DOM元素捕获
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 创建占位符
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.strokeStyle = '#ccc';
        ctx.strokeRect(0, 0, canvas.width, canvas.height);

        return canvas;
    }

    // 主导出函数
    async function exportLayeredPSD() {
        try {
            console.log('🎯 开始分层PSD导出...');

            // 获取设计数据
            const designData = getGaodingData();
            if (!designData || !designData.elements || designData.elements.length === 0) {
                throw new Error('未找到可导出的设计元素');
            }

            console.log(`📐 画布: ${designData.width}x${designData.height}`);
            console.log(`🎨 元素: ${designData.elements.length}个`);

            // 处理每个元素为独立图层
            const layers = [];
            for (let i = 0; i < designData.elements.length; i++) {
                const element = designData.elements[i];
                console.log(`🔄 处理 ${i+1}/${designData.elements.length}: ${element.type} - ${element.name}`);

                try {
                    const canvas = await createLayerCanvas(element);
                    if (canvas && canvas.width > 0 && canvas.height > 0) {
                        layers.push({
                            name: element.name || `${element.type}_${i+1}`,
                            left: element.left || 0,
                            top: element.top || 0,
                            right: (element.left || 0) + element.width,
                            bottom: (element.top || 0) + element.height,
                            opacity: 255,
                            blendMode: 'normal',
                            canvas: canvas
                        });
                        console.log(`✅ ${element.name} 处理成功`);
                    }
                } catch (e) {
                    console.warn(`⚠️ ${element.name} 处理失败:`, e);
                }
            }

            if (layers.length === 0) {
                throw new Error('没有成功处理任何图层');
            }

            // 构建PSD
            const psdData = {
                width: designData.width,
                height: designData.height,
                channels: 4,
                bitsPerChannel: 8,
                colorMode: 3,
                children: layers.reverse() // PSD图层顺序是反的
            };

            console.log(`🔨 生成PSD: ${layers.length}个图层`);
            const psdBuffer = window.agPsd.writePsd(psdData);

            // 下载
            const blob = new Blob([psdBuffer], { type: 'application/octet-stream' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `稿定设计_分层_${Date.now()}.psd`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            console.log('🎉 分层PSD导出成功！');
            alert(`✅ 分层PSD导出成功！\n\n包含 ${layers.length} 个独立图层\n文件大小: ${(blob.size/1024/1024).toFixed(2)} MB\n\n每个图层都可以在Photoshop中独立编辑！`);

        } catch (error) {
            console.error('❌ 导出失败:', error);
            alert(`❌ 导出失败: ${error.message}`);
        }
    }

    // 创建导出按钮
    function createExportButton() {
        const button = document.createElement('div');
        button.innerHTML = '🎨 分层导出';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 10000;
            user-select: none;
        `;

        button.addEventListener('click', async () => {
            if (isProcessing) {
                alert('⏳ 正在处理中...');
                return;
            }

            isProcessing = true;
            button.innerHTML = '⏳ 导出中...';
            button.style.background = '#FF9800';

            try {
                await exportLayeredPSD();
            } finally {
                isProcessing = false;
                button.innerHTML = '🎨 分层导出';
                button.style.background = '#4CAF50';
            }
        });

        document.body.appendChild(button);
        console.log('✅ 分层导出按钮已创建');
    }

    // 初始化
    async function init() {
        console.log('🚀 稿定设计真正分层导出工具启动');

        await waitForAgPsd();

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createExportButton);
        } else {
            setTimeout(createExportButton, 1000);
        }
    }

    init();
})();
