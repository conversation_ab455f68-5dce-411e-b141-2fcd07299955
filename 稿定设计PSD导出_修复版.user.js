// ==UserScript==
// @name         稿定设计PSD导出工具 - 修复版
// @namespace    http://tampermonkey.net/
// @version      2.1
// @description  为稿定设计添加真正的PSD文件导出功能，修复兼容性问题
// <AUTHOR>
// @match        https://www.gaoding.com/editor/*
// @match        https://gaoding.com/editor/*
// @grant        none
// @require      https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
// @require      https://unpkg.com/ag-psd@14.3.11/dist/bundle.js
// ==/UserScript==

(function() {
    'use strict';

    let isInitialized = false;

    // 等待元素出现
    function waitForElement(selector, timeout = 15000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`未找到元素: ${selector}`));
                } else {
                    setTimeout(check, 200);
                }
            }
            check();
        });
    }

    // 显示消息提示
    function showMessage(message, type = 'info', duration = 4000) {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-size: 14px;
            z-index: 100000;
            max-width: 350px;
            word-wrap: break-word;
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            ${type === 'success' ? 'background: linear-gradient(135deg, #52c41a, #73d13d);' : 
              type === 'error' ? 'background: linear-gradient(135deg, #ff4d4f, #ff7875);' : 
              'background: linear-gradient(135deg, #1890ff, #40a9ff);'}
        `;
        messageDiv.innerHTML = message.replace(/\n/g, '<br>');
        document.body.appendChild(messageDiv);

        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateX(100%)';
                messageDiv.style.transition = 'all 0.3s ease';
                setTimeout(() => messageDiv.remove(), 300);
            }
        }, duration);
    }

    // 获取最佳画布
    function getBestCanvas() {
        const canvases = Array.from(document.querySelectorAll('canvas'));
        console.log('找到画布数量:', canvases.length);
        
        // 过滤掉太小的画布
        const validCanvases = canvases.filter(canvas => 
            canvas.width > 100 && canvas.height > 100
        );

        if (validCanvases.length === 0) {
            throw new Error('未找到有效的画布');
        }

        // 选择最大的画布
        const bestCanvas = validCanvases.reduce((largest, current) => {
            const largestArea = largest.width * largest.height;
            const currentArea = current.width * current.height;
            return currentArea > largestArea ? current : largest;
        });

        console.log('选择的画布:', bestCanvas.className, '尺寸:', bestCanvas.width, 'x', bestCanvas.height);
        return bestCanvas;
    }

    // 简化的PSD生成器
    class SimplePSDGenerator {
        constructor() {
            this.layers = [];
            this.width = 0;
            this.height = 0;
        }

        setCanvasSize(width, height) {
            this.width = width;
            this.height = height;
        }

        addLayer(name, canvas, x = 0, y = 0, opacity = 1) {
            // 验证canvas
            if (!canvas || typeof canvas.getContext !== 'function') {
                console.warn('无效的canvas对象:', name);
                return;
            }

            this.layers.push({
                name: name,
                canvas: canvas,
                left: Math.round(x),
                top: Math.round(y),
                right: Math.round(x + canvas.width),
                bottom: Math.round(y + canvas.height),
                opacity: Math.round(opacity * 255),
                blendMode: 'normal'
            });
            
            console.log(`添加图层: ${name}, 尺寸: ${canvas.width}x${canvas.height}, 位置: (${x}, ${y})`);
        }

        async generatePSD() {
            if (!window.agPsd) {
                throw new Error('PSD库未加载完成，请刷新页面重试');
            }

            if (this.layers.length === 0) {
                throw new Error('没有可导出的图层');
            }

            try {
                // 创建PSD文档结构
                const psd = {
                    width: this.width,
                    height: this.height,
                    channels: 3,
                    bitsPerChannel: 8,
                    colorMode: 3, // RGB
                    children: this.layers.map(layer => ({
                        name: layer.name,
                        opacity: layer.opacity,
                        blendMode: layer.blendMode,
                        left: layer.left,
                        top: layer.top,
                        right: layer.right,
                        bottom: layer.bottom,
                        canvas: layer.canvas
                    }))
                };

                console.log('生成PSD结构:', {
                    width: psd.width,
                    height: psd.height,
                    layerCount: psd.children.length
                });
                
                // 生成PSD文件
                const arrayBuffer = window.agPsd.writePsd(psd);
                return new Blob([arrayBuffer], { type: 'application/octet-stream' });
                
            } catch (error) {
                console.error('PSD生成详细错误:', error);
                throw new Error(`PSD生成失败: ${error.message}`);
            }
        }
    }

    // 导出PNG
    async function exportPNG() {
        try {
            showMessage('正在生成PNG图片...', 'info');
            
            const canvas = getBestCanvas();
            
            const link = document.createElement('a');
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            link.download = `稿定设计_${timestamp}.png`;
            link.href = canvas.toDataURL('image/png', 1.0);
            link.click();
            
            showMessage('✅ PNG导出成功！', 'success');
        } catch (error) {
            console.error('PNG导出失败:', error);
            showMessage('❌ PNG导出失败: ' + error.message, 'error');
        }
    }

    // 导出PSD文件
    async function exportPSD() {
        try {
            showMessage('正在分析页面结构...\n这可能需要几秒钟时间', 'info', 6000);
            
            const canvas = getBestCanvas();
            const psdGen = new SimplePSDGenerator();
            psdGen.setCanvasSize(canvas.width, canvas.height);

            // 添加主画布作为背景
            const backgroundCanvas = document.createElement('canvas');
            backgroundCanvas.width = canvas.width;
            backgroundCanvas.height = canvas.height;
            const bgCtx = backgroundCanvas.getContext('2d');
            
            // 复制主画布内容
            bgCtx.drawImage(canvas, 0, 0);
            psdGen.addLayer('背景', backgroundCanvas, 0, 0, 1);

            let layerCount = 1;

            // 尝试从稿定设计的特定结构提取图层
            await extractGaodingLayers(psdGen, canvas);

            // 如果没有提取到额外图层，尝试通用方法
            if (psdGen.layers.length === 1) {
                console.log('使用通用方法提取图层');
                
                // 方法1: 提取可见的图片元素
                const images = document.querySelectorAll('img[src]');
                console.log('找到图片元素:', images.length);
                
                for (let i = 0; i < Math.min(images.length, 10); i++) {
                    const img = images[i];
                    const rect = img.getBoundingClientRect();
                    const canvasRect = canvas.getBoundingClientRect();
                    
                    // 检查图片是否在画布区域内
                    if (rect.width > 30 && rect.height > 30 && 
                        rect.left >= canvasRect.left && rect.top >= canvasRect.top &&
                        rect.right <= canvasRect.right && rect.bottom <= canvasRect.bottom) {
                        
                        try {
                            const imgCanvas = document.createElement('canvas');
                            imgCanvas.width = img.naturalWidth || rect.width;
                            imgCanvas.height = img.naturalHeight || rect.height;
                            const imgCtx = imgCanvas.getContext('2d');
                            
                            if (img.complete && img.naturalWidth > 0) {
                                imgCtx.drawImage(img, 0, 0, imgCanvas.width, imgCanvas.height);
                                
                                // 计算相对于画布的位置
                                const relativeX = rect.left - canvasRect.left;
                                const relativeY = rect.top - canvasRect.top;
                                
                                psdGen.addLayer(`图片${layerCount}`, imgCanvas, relativeX, relativeY, 1);
                                layerCount++;
                            }
                        } catch (e) {
                            console.warn('图片处理失败:', e);
                        }
                    }
                }

                // 方法2: 使用html2canvas截取特定区域
                const designArea = document.querySelector('.infinite-canvas')?.parentElement;
                if (designArea && layerCount < 5) {
                    try {
                        const areaCanvas = await html2canvas(designArea, {
                            backgroundColor: null,
                            scale: 0.5,
                            useCORS: true,
                            allowTaint: true
                        });
                        
                        if (areaCanvas.width > 0 && areaCanvas.height > 0) {
                            psdGen.addLayer(`设计区域`, areaCanvas, 0, 0, 0.8);
                            layerCount++;
                        }
                    } catch (e) {
                        console.warn('设计区域截取失败:', e);
                    }
                }
            }

            showMessage('正在生成PSD文件...\n请稍候', 'info', 8000);
            
            // 生成PSD
            const psdBlob = await psdGen.generatePSD();
            
            // 下载文件
            const link = document.createElement('a');
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            link.download = `稿定设计_${timestamp}.psd`;
            link.href = URL.createObjectURL(psdBlob);
            link.click();
            
            showMessage(`✅ PSD文件导出成功！\n包含 ${psdGen.layers.length} 个图层\n可在Photoshop中打开编辑`, 'success', 5000);
            
        } catch (error) {
            console.error('PSD导出失败:', error);
            showMessage('❌ PSD导出失败:\n' + error.message, 'error', 6000);
        }
    }

    // 尝试从稿定设计特定结构提取图层
    async function extractGaodingLayers(psdGen, mainCanvas) {
        // 查找稿定设计可能的图层容器
        const layerContainers = [
            '.infinite-canvas',
            '[class*="layer"]',
            '[class*="element"]',
            '[data-element-id]'
        ];

        for (let selector of layerContainers) {
            const elements = document.querySelectorAll(selector);
            console.log(`查找 ${selector}:`, elements.length);
            
            if (elements.length > 1) { // 排除主画布本身
                for (let i = 1; i < Math.min(elements.length, 6); i++) {
                    const element = elements[i];
                    const rect = element.getBoundingClientRect();
                    
                    if (rect.width > 20 && rect.height > 20) {
                        try {
                            const elementCanvas = await html2canvas(element, {
                                backgroundColor: null,
                                scale: 1,
                                useCORS: true,
                                allowTaint: true
                            });
                            
                            if (elementCanvas.width > 0 && elementCanvas.height > 0) {
                                psdGen.addLayer(`元素${i}`, elementCanvas, rect.left, rect.top, 1);
                            }
                        } catch (e) {
                            console.warn('元素处理失败:', e);
                        }
                    }
                }
                break; // 找到一种有效的选择器就停止
            }
        }
    }

    // 创建导出按钮
    function createExportButtons() {
        if (document.getElementById('psd-export-panel-fixed')) {
            return;
        }

        const panel = document.createElement('div');
        panel.id = 'psd-export-panel-fixed';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 99999;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            min-width: 180px;
        `;

        const title = document.createElement('div');
        title.textContent = '🎨 PSD导出工具';
        title.style.cssText = `
            font-size: 16px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        `;

        const buttonStyle = `
            width: 100%;
            padding: 12px;
            margin-bottom: 10px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        `;

        const pngBtn = document.createElement('button');
        pngBtn.textContent = '📷 导出PNG';
        pngBtn.style.cssText = buttonStyle + `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        `;
        pngBtn.onclick = exportPNG;

        const psdBtn = document.createElement('button');
        psdBtn.textContent = '🎨 导出PSD';
        psdBtn.style.cssText = buttonStyle + `
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        `;
        psdBtn.onclick = exportPSD;

        // 添加悬停效果
        [pngBtn, psdBtn].forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-3px)';
                btn.style.boxShadow = '0 6px 20px rgba(0,0,0,0.25)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'translateY(0)';
                btn.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            });
        });

        panel.appendChild(title);
        panel.appendChild(pngBtn);
        panel.appendChild(psdBtn);
        document.body.appendChild(panel);
    }

    // 初始化
    async function init() {
        if (isInitialized) return;
        
        try {
            console.log('PSD导出工具初始化中...');
            
            // 等待ag-psd库加载
            let attempts = 0;
            while (!window.agPsd && attempts < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (!window.agPsd) {
                throw new Error('PSD库加载失败');
            }
            
            await waitForElement('canvas');
            
            setTimeout(() => {
                createExportButtons();
                showMessage('🎉 PSD导出工具已就绪！\n支持真正的PSD格式导出', 'success');
                isInitialized = true;
            }, 2000);
            
        } catch (error) {
            console.error('初始化失败:', error);
            showMessage('❌ 初始化失败:\n' + error.message + '\n请刷新页面重试', 'error');
        }
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    console.log('稿定设计PSD导出工具(修复版)已加载');

})();
