// ==UserScript==
// @name         稿定设计VIP素材下载器
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  在稿定设计网站的素材、模板上添加下载按钮，尝试使用会员权限下载高清无水印素材。
// <AUTHOR>
// @match        https://www.gaoding.com/*
// @icon         https://www.gaoding.com/favicon.ico
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// @require      https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js
// ==/UserScript==

(function() {
    'use strict';

    // ========================
    // 配置与日志
    // ========================
    const CONFIG = {
        debug: true,
        downloadButtonClass: 'vip-material-download-button'
    };

    const logger = {
        log: (msg, ...args) => CONFIG.debug && console.log(`[稿定VIP下载] ${msg}`, ...args),
        error: (msg, ...args) => console.error(`[稿定VIP下载] ${msg}`, ...args),
    };

    // ========================
    // 样式注入
    // ========================
    GM_addStyle(`
        .${CONFIG.downloadButtonClass} {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 1000;
            background-color: #2878ff;
            color: white !important;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s, transform 0.2s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transform: scale(0.95);
            opacity: 0.8;
        }
        .${CONFIG.downloadButtonClass}:hover {
            background-color: #4a90fe;
            transform: scale(1);
            opacity: 1;
        }
        .${CONFIG.downloadButtonClass}:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            opacity: 0.7;
        }
        /* 使父容器可以定位按钮 */
        .tpl-item, .components-list__item, .material-card-wrapper, .psd-template-card, .image-item-wrapper {
            position: relative;
        }
    `);

    // ========================
    // 核心下载功能
    // ========================
    function downloadAsset(url, filename) {
        return new Promise((resolve, reject) => {
            logger.log(`请求下载: ${url}`);
            GM_xmlhttpRequest({
                method: 'GET',
                url: url,
                responseType: 'blob',
                headers: {
                    'Referer': 'https://www.gaoding.com/'
                },
                onload: function(response) {
                    if (response.status >= 200 && response.status < 300) {
                        logger.log('下载成功, 保存文件...');
                        saveAs(response.response, filename);
                        resolve();
                    } else {
                        logger.error(`下载失败，状态码: ${response.status} for ${url}`);
                        alert(`下载 ${filename} 失败，服务器返回错误。`);
                        reject(new Error(`Status ${response.status}`));
                    }
                },
                onerror: function(error) {
                    logger.error('下载请求出错', error);
                    alert(`下载 ${filename} 失败，网络请求错误。`);
                    reject(error);
                }
            });
        });
    }

    // ========================
    // 查找素材并注入按钮
    // ========================
    function findAndAddDownloadButtons() {
        // 这些选择器是根据稿定网站常见结构编写的，可能需要随网站更新而调整
        const itemSelectors = [
            '.tpl-item',                 // 模板
            '.components-list__item',    // 组件/元素
            '.material-card-wrapper',    // 素材卡片
            '.psd-template-card',        // PSD模板
            '.image-item-wrapper',       // 图片搜索结果
            '[class*="image-item"]',     // 各种图片项
            '[class*="template-item"]'   // 各种模板项
        ];

        document.querySelectorAll(itemSelectors.join(', ')).forEach(item => {
            if (item.querySelector(`.${CONFIG.downloadButtonClass}`) || item.getAttribute('data-download-button-injected')) {
                return; // 如果按钮已存在或已处理过，则跳过
            }

            item.setAttribute('data-download-button-injected', 'true'); // 标记为已处理

            let imageUrl = null;
            let filename = `gaoding_asset_${Date.now()}`;

            // 策略1: 查找 <img> 标签
            const img = item.querySelector('img[src*="gding.com"], img[src*="gd-file.cn"]');
            if (img && img.src) {
                imageUrl = img.src;
            }

            // 策略2: 如果没有img，查找背景图
            if (!imageUrl) {
                // 查找所有子元素，看谁有背景图
                const elements = Array.from(item.getElementsByTagName('*'));
                elements.unshift(item); // 把父元素也加进去
                for (const el of elements) {
                     if (el.style.backgroundImage.includes('gding.com') || el.style.backgroundImage.includes('gd-file.cn')) {
                         const urlMatch = el.style.backgroundImage.match(/url\("?(.+?)"?\)/);
                         if (urlMatch && urlMatch[1]) {
                             imageUrl = urlMatch[1];
                             break;
                         }
                     }
                }
            }

            if (imageUrl) {
                // 清理URL，移除用于生成缩略图的参数
                const originalUrl = imageUrl.split('?')[0];
                const extensionMatch = originalUrl.match(/\.(jpg|jpeg|png|gif|svg|webp)/i);
                const extension = extensionMatch ? extensionMatch[1] : 'png';
                filename = `${filename}.${extension}`;

                // 创建下载按钮
                const button = document.createElement('button');
                button.innerText = '下载';
                button.className = CONFIG.downloadButtonClass;

                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation(); // 阻止点击卡片本身
                    button.innerText = '下载中...';
                    button.disabled = true;

                    downloadAsset(originalUrl, filename)
                        .catch(err => logger.error("下载过程中断：", err))
                        .finally(() => {
                            button.innerText = '下载';
                            button.disabled = false;
                        });
                });

                item.appendChild(button);
            }
        });
    }

    // ========================
    // 使用 MutationObserver 监控动态内容
    // ========================
    function debounce(func, delay) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
        };
    }

    const debouncedAddButtons = debounce(findAndAddDownloadButtons, 500);

    const observer = new MutationObserver((mutationsList) => {
        // 简单高效地检查是否有节点添加
        for(const mutation of mutationsList) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                debouncedAddButtons();
                return; // 一旦检测到变化就执行，无需重复
            }
        }
    });

    function startObserver() {
        const targetNode = document.body;
        const config = { childList: true, subtree: true };
        observer.observe(targetNode, config);
        logger.log('素材监视器已启动。');
    }

    // ========================
    // 初始化
    // ========================
    logger.log('稿定设计VIP素材下载器已启动。');

    // 页面加载完成后开始运行
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        findAndAddDownloadButtons(); // 首次运行
        startObserver();
    } else {
        window.addEventListener('load', () => {
            findAndAddDownloadButtons(); // 首次运行
            startObserver();
        });
    }

})(); 