// ==UserScript==
// @name         稿定设计VIP素材下载器
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  在稿定设计网站的素材、模板上添加下载按钮，尝试使用会员权限下载高清无水印素材。
// <AUTHOR>
// @match        https://www.gaoding.com/*
// @icon         https://www.gaoding.com/favicon.ico
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// @require      https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js
// ==/UserScript==

(function() {
    'use strict';

    // ========================
    // 配置与日志
    // ========================
    const CONFIG = {
        debug: true,
        downloadButtonClass: 'vip-material-download-button'
    };

    const logger = {
        log: (msg, ...args) => CONFIG.debug && console.log(`[稿定VIP下载] ${msg}`, ...args),
        error: (msg, ...args) => console.error(`[稿定VIP下载] ${msg}`, ...args),
    };

    // ========================
    // 样式注入
    // ========================
    GM_addStyle(`
        .${CONFIG.downloadButtonClass} {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 1000;
            background-color: #2878ff;
            color: white !important;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s, transform 0.2s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transform: scale(0.95);
            opacity: 0.8;
        }
        .${CONFIG.downloadButtonClass}:hover {
            background-color: #4a90fe;
            transform: scale(1);
            opacity: 1;
        }
        .${CONFIG.downloadButtonClass}:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            opacity: 0.7;
        }
        /* 使父容器可以定位按钮 */
        [class*="tpl-item"], [class*="components-list__item"], [class*="material-card-wrapper"], [class*="psd-template-card"], [class*="image-item-wrapper"], [class*="card-wrapper"], [class*="work-item"], [class*="image-item"], [class*="template-item"] {
            position: relative !important;
        }
    `);

    // ========================
    // 核心下载功能
    // ========================
    function downloadAsset(url, filename) {
        return new Promise((resolve, reject) => {
            logger.log(`请求下载: ${url}`);
            GM_xmlhttpRequest({
                method: 'GET',
                url: url,
                responseType: 'blob',
                headers: {
                    'Referer': 'https://www.gaoding.com/'
                },
                onload: function(response) {
                    if (response.status >= 200 && response.status < 300) {
                        logger.log('下载成功, 保存文件...');
                        saveAs(response.response, filename);
                        resolve();
                    } else {
                        logger.error(`下载失败，状态码: ${response.status} for ${url}`);
                        alert(`下载 ${filename} 失败，服务器返回错误。`);
                        reject(new Error(`Status ${response.status}`));
                    }
                },
                onerror: function(error) {
                    logger.error('下载请求出错', error);
                    alert(`下载 ${filename} 失败，网络请求错误。`);
                    reject(error);
                }
            });
        });
    }

    // ========================
    // 查找素材并注入按钮
    // ========================
    function findAndAddDownloadButtons() {
        // 使用更通用和健壮的选择器，以适应网站的更新
        const itemSelectors = [
            '[class*="tpl-item"]',                 // 模板
            '[class*="components-list__item"]',    // 组件/元素
            '[class*="material-card-wrapper"]',    // 素材卡片
            '[class*="psd-template-card"]',        // PSD模板
            '[class*="image-item-wrapper"]',       // 图片搜索结果
            '[class*="card-wrapper"]',             // 通用卡片包装器
            '[class*="work-item"]',                // 作品项
            '[class*="image-item"]',               // 通用图片项
            '[class*="template-item"]'             // 通用模板项
        ];

        document.querySelectorAll(itemSelectors.join(', ')).forEach(item => {
            if (item.querySelector(`.${CONFIG.downloadButtonClass}`) || item.getAttribute('data-download-button-injected')) {
                return; // 如果按钮已存在或已处理过，则跳过
            }

            item.setAttribute('data-download-button-injected', 'true');

            let imageUrl = null;
            let filename = `gaoding_asset_${Date.now()}`;

            // 策略1: 查找 <img> 标签 (包括懒加载的 data-src)
            const img = item.querySelector('img[src*="gding.com"], img[src*="gd-file.cn"], img[data-src*="gding.com"], img[data-src*="gd-file.cn"]');
            if (img) {
                imageUrl = img.dataset.src || img.src;
            }

            // 策略2: 查找 <picture> 中的 <source> 标签
            if (!imageUrl) {
                const source = item.querySelector('picture > source[srcset*="gding.com"], picture > source[srcset*="gd-file.cn"]');
                if (source && source.srcset) {
                    imageUrl = source.srcset.split(',')[0].trim().split(' ')[0]; // 取 srcset 中的第一个 URL
                }
            }

            // 策略3: 查找背景图 (background-image)
            if (!imageUrl) {
                const elementsWithBg = [item, ...Array.from(item.querySelectorAll('*'))];
                for (const el of elementsWithBg) {
                     if (el.style && (el.style.backgroundImage.includes('gding.com') || el.style.backgroundImage.includes('gd-file.cn'))) {
                         const urlMatch = el.style.backgroundImage.match(/url\("?(.+?)"?\)/);
                         if (urlMatch && urlMatch[1]) {
                             imageUrl = urlMatch[1];
                             break;
                         }
                     }
                }
            }

            if (imageUrl && !imageUrl.startsWith('data:')) { // 确保不是base64编码的图片
                // 智能清理URL，移除缩略图参数和路径标记
                let originalUrl = imageUrl.split('?')[0];
                originalUrl = originalUrl.replace(/(\-|_)(thumb|small|preview|webp|\d+x\d+)\./, '.');

                const extensionMatch = originalUrl.match(/\.(jpg|jpeg|png|gif|svg|webp)/i);
                const extension = extensionMatch ? extensionMatch[1] : 'png';
                filename = `${filename}.${extension}`;

                // 创建下载按钮
                const button = document.createElement('button');
                button.innerText = '下载';
                button.className = CONFIG.downloadButtonClass;

                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    button.innerText = '下载中...';
                    button.disabled = true;

                    downloadAsset(originalUrl, filename)
                        .then(() => {
                            button.innerText = '成功!';
                            button.style.backgroundColor = '#2ecc71'; // 绿色表示成功
                            setTimeout(() => {
                                button.innerText = '下载';
                                button.disabled = false;
                                button.style.backgroundColor = ''; // 恢复原色
                            }, 2000);
                        })
                        .catch(err => {
                            logger.error("下载过程中断：", err);
                            button.innerText = '失败';
                            button.style.backgroundColor = '#e74c3c'; // 红色表示失败
                            setTimeout(() => {
                                button.innerText = '下载';
                                button.disabled = false;
                                button.style.backgroundColor = ''; // 恢复原色
                            }, 3000);
                        });
                });

                item.appendChild(button);
            }
        });
    }

    // ========================
    // 使用 MutationObserver 监控动态内容
    // ========================
    function debounce(func, delay) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
        };
    }

    const debouncedAddButtons = debounce(findAndAddDownloadButtons, 500);

    const observer = new MutationObserver((mutationsList) => {
        // 简单高效地检查是否有节点添加
        for(const mutation of mutationsList) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                debouncedAddButtons();
                return; // 一旦检测到变化就执行，无需重复
            }
        }
    });

    function startObserver() {
        const targetNode = document.body;
        const config = { childList: true, subtree: true };
        observer.observe(targetNode, config);
        logger.log('素材监视器已启动。');
    }

    // ========================
    // 初始化
    // ========================
    logger.log('稿定设计VIP素材下载器已启动。');

    // 页面加载完成后开始运行
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        findAndAddDownloadButtons(); // 首次运行
        startObserver();
    } else {
        window.addEventListener('load', () => {
            findAndAddDownloadButtons(); // 首次运行
            startObserver();
        });
    }

})(); 