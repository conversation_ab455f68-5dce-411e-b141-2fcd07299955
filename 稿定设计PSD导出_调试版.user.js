// ==UserScript==
// @name         稿定设计PSD导出工具 - 调试版
// @namespace    http://tampermonkey.net/
// @version      2.2
// @description  专门用于调试PSD导出问题的版本
// <AUTHOR>
// @match        https://www.gaoding.com/editor/*
// @match        https://gaoding.com/editor/*
// @grant        none
// @require      https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
// @require      https://unpkg.com/ag-psd@14.3.11/dist/bundle.js
// ==/UserScript==

(function() {
    'use strict';

    // 等待页面加载完成
    function waitForLibraries() {
        return new Promise((resolve) => {
            function check() {
                if (window.html2canvas && window.agPsd) {
                    console.log('✅ 所有库已加载完成');
                    resolve();
                } else {
                    console.log('⏳ 等待库加载...', {
                        html2canvas: !!window.html2canvas,
                        agPsd: !!window.agPsd
                    });
                    setTimeout(check, 500);
                }
            }
            check();
        });
    }

    // 简化的PSD生成器
    class DebugPSDGenerator {
        constructor() {
            this.layers = [];
            this.width = 800;
            this.height = 600;
        }

        setCanvasSize(width, height) {
            this.width = width;
            this.height = height;
            console.log(`设置画布尺寸: ${width}x${height}`);
        }

        addLayer(name, canvas, x = 0, y = 0, opacity = 1) {
            if (!canvas || typeof canvas.getContext !== 'function') {
                console.warn('❌ 无效的canvas:', name);
                return;
            }

            console.log(`➕ 添加图层: ${name}, 尺寸: ${canvas.width}x${canvas.height}, 位置: (${x}, ${y})`);
            
            this.layers.push({
                name: name,
                canvas: canvas,
                left: Math.round(x),
                top: Math.round(y),
                right: Math.round(x + canvas.width),
                bottom: Math.round(y + canvas.height),
                opacity: Math.round(opacity * 255),
                blendMode: 'normal'
            });
        }

        async generatePSD() {
            console.log('🚀 开始生成PSD文件...');
            
            if (!window.agPsd) {
                throw new Error('ag-psd库未加载');
            }

            if (this.layers.length === 0) {
                throw new Error('没有图层可导出');
            }

            try {
                // 创建主画布
                const mainCanvas = document.createElement('canvas');
                mainCanvas.width = this.width;
                mainCanvas.height = this.height;
                const mainCtx = mainCanvas.getContext('2d');
                
                // 白色背景
                mainCtx.fillStyle = '#FFFFFF';
                mainCtx.fillRect(0, 0, this.width, this.height);

                // 合成所有图层到主画布
                this.layers.forEach((layer, index) => {
                    console.log(`🎨 合成图层 ${index + 1}: ${layer.name}`);
                    mainCtx.globalAlpha = layer.opacity / 255;
                    mainCtx.drawImage(layer.canvas, layer.left, layer.top);
                    mainCtx.globalAlpha = 1.0;
                });

                // 创建PSD结构
                const psd = {
                    width: this.width,
                    height: this.height,
                    channels: 3,
                    bitsPerChannel: 8,
                    colorMode: 3, // RGB
                    canvas: mainCanvas,
                    children: this.layers.map(layer => ({
                        name: layer.name,
                        opacity: layer.opacity,
                        blendMode: 'normal',
                        left: layer.left,
                        top: layer.top,
                        right: layer.right,
                        bottom: layer.bottom,
                        canvas: layer.canvas
                    }))
                };

                console.log('📋 PSD结构:', {
                    width: psd.width,
                    height: psd.height,
                    layerCount: psd.children.length,
                    layers: psd.children.map(l => l.name)
                });

                // 生成PSD文件
                console.log('💾 写入PSD文件...');
                const arrayBuffer = window.agPsd.writePsd(psd);
                console.log(`✅ PSD生成成功! 文件大小: ${arrayBuffer.byteLength} bytes`);
                
                return new Blob([arrayBuffer], { type: 'application/octet-stream' });

            } catch (error) {
                console.error('❌ PSD生成失败:', error);
                throw error;
            }
        }
    }

    // 获取最佳画布
    function getBestCanvas() {
        const selectors = [
            '.infinite-canvas',
            'canvas[width][height]',
            '.canvas-container canvas',
            '.editor-canvas'
        ];

        for (const selector of selectors) {
            const canvas = document.querySelector(selector);
            if (canvas && canvas.width > 0 && canvas.height > 0) {
                console.log(`✅ 找到画布: ${selector}, 尺寸: ${canvas.width}x${canvas.height}`);
                return canvas;
            }
        }

        throw new Error('未找到有效的画布');
    }

    // 简单的图层提取
    async function extractSimpleLayers(psdGen) {
        console.log('🔍 开始提取图层...');
        
        const canvas = getBestCanvas();
        const canvasRect = canvas.getBoundingClientRect();
        let layerCount = 0;

        // 查找图片元素
        const images = document.querySelectorAll('img[src]');
        console.log(`找到 ${images.length} 个图片元素`);

        for (let i = 0; i < Math.min(images.length, 5); i++) {
            const img = images[i];
            const rect = img.getBoundingClientRect();

            if (rect.width > 10 && rect.height > 10) {
                try {
                    const imgCanvas = await html2canvas(img, {
                        backgroundColor: null,
                        scale: 1,
                        useCORS: true,
                        allowTaint: true
                    });

                    if (imgCanvas.width > 0 && imgCanvas.height > 0) {
                        const x = Math.max(0, rect.left - canvasRect.left);
                        const y = Math.max(0, rect.top - canvasRect.top);
                        
                        psdGen.addLayer(`图片${i + 1}`, imgCanvas, x, y, 1);
                        layerCount++;
                    }
                } catch (e) {
                    console.warn(`图片 ${i + 1} 处理失败:`, e.message);
                }
            }
        }

        return layerCount;
    }

    // 导出PSD
    async function exportPSD() {
        try {
            console.log('🎯 开始PSD导出流程...');
            
            const canvas = getBestCanvas();
            const psdGen = new DebugPSDGenerator();
            psdGen.setCanvasSize(canvas.width, canvas.height);

            // 添加背景
            const bgCanvas = document.createElement('canvas');
            bgCanvas.width = canvas.width;
            bgCanvas.height = canvas.height;
            const bgCtx = bgCanvas.getContext('2d');
            bgCtx.drawImage(canvas, 0, 0);
            psdGen.addLayer('背景', bgCanvas, 0, 0, 1);

            // 提取图层
            const layerCount = await extractSimpleLayers(psdGen);
            console.log(`📊 总共提取了 ${layerCount + 1} 个图层`);

            // 生成PSD
            const blob = await psdGen.generatePSD();
            
            // 下载文件
            const link = document.createElement('a');
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            link.download = `稿定设计_调试_${timestamp}.psd`;
            link.href = URL.createObjectURL(blob);
            link.click();
            
            console.log('🎉 PSD导出完成!');
            alert('✅ PSD文件导出成功！请检查下载文件夹。');

        } catch (error) {
            console.error('💥 导出失败:', error);
            alert('❌ 导出失败: ' + error.message);
        }
    }

    // 创建调试按钮
    function createDebugButton() {
        const button = document.createElement('button');
        button.textContent = '🐛 调试PSD导出';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            padding: 10px 15px;
            background: #ff6b35;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        `;
        
        button.onclick = exportPSD;
        document.body.appendChild(button);
        console.log('🔧 调试按钮已创建');
    }

    // 初始化
    async function init() {
        console.log('🚀 稿定设计PSD导出调试版启动...');
        
        await waitForLibraries();
        
        // 等待页面稳定
        setTimeout(() => {
            createDebugButton();
            console.log('✅ 调试版初始化完成');
        }, 2000);
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
