// ==UserScript==
// @name         稿定设计高级PSD导出工具
// @namespace    http://tampermonkey.net/
// @version      1.1.0
// @description  为稿定设计网站添加高级PSD导出功能，支持多图层、文字图层、位置保持、透明度等。现使用ag-psd库生成真实PSD文件。
// <AUTHOR>
// @match        https://www.gaoding.com/editor/design*
// @icon         https://www.gaoding.com/favicon.ico
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// @grant        unsafeWindow
// @grant        GM_setValue
// @grant        GM_getValue
// @connect      *
// @require      https://cdn.jsdelivr.net/npm/ag-psd/dist/bundle.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
// ==/UserScript==

(function() {
    'use strict';

    // ========================
    // 常量和配置
    // ========================
    const CONFIG = {
        debug: true,
        exportQuality: 1.0, // 图层导出质量 (0.0-1.0)
        defaultPsdName: '稿定设计导出',
        uiElements: {
            buttonId: 'gd-psd-export-button',
            modalId: 'gd-psd-export-modal',
            progressBarId: 'gd-psd-export-progress'
        },
        selectors: {
            canvas: '.editor-canvas-container canvas',
            layersPanel: '.editor-layers-panel',
            layerItem: '.layer-item',
            layerName: '.layer-name',
            textLayer: '.text-layer',
            imageLayer: '.image-layer'
        }
    };

    // ========================
    // 日志和调试工具
    // ========================
    const logger = {
        log: (msg, ...args) => CONFIG.debug && console.log(`[稿定设计PSD导出] ${msg}`, ...args),
        info: (msg, ...args) => console.info(`[稿定设计PSD导出] ${msg}`, ...args),
        error: (msg, ...args) => console.error(`[稿定设计PSD导出] ${msg}`, ...args),
        warn: (msg, ...args) => console.warn(`[稿定设计PSD导出] ${msg}`, ...args),
    };

    // ========================
    // 用户界面相关
    // ========================
    
    // 添加CSS样式
    GM_addStyle(`
        /* 导出按钮样式 (悬浮按钮) */
        #${CONFIG.uiElements.buttonId} {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background-color: #2878ff;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 18px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            z-index: 99999;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
        }
        
        #${CONFIG.uiElements.buttonId}:hover {
            background-color: #4a90fe;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        #gd-original-image-download-button {
            position: fixed;
            bottom: 90px;
            right: 30px;
            background-color: #00c482;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 18px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 99999;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
        }

        #gd-original-image-download-button:hover {
            background-color: #00d68f;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        #${CONFIG.uiElements.buttonId}.exporting {
            background-color: #cccccc;
            cursor: wait;
            transform: translateY(0);
        }
        
        /* 导出模态框样式 */
        #${CONFIG.uiElements.modalId} {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 20px;
            z-index: 10000;
            width: 400px;
            display: none;
        }
        
        #${CONFIG.uiElements.modalId} h3 {
            margin-top: 0;
            color: #333;
        }
        
        #${CONFIG.uiElements.modalId} .content {
            margin: 15px 0;
        }
        
        #${CONFIG.uiElements.modalId} .actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        #${CONFIG.uiElements.modalId} button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        #${CONFIG.uiElements.modalId} button.cancel {
            background-color: #f5f5f5;
            color: #666;
        }
        
        #${CONFIG.uiElements.modalId} button.confirm {
            background-color: #2878ff;
            color: white;
        }
        
        /* 进度条样式 */
        #${CONFIG.uiElements.progressBarId} {
            width: 100%;
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            margin-top: 15px;
            overflow: hidden;
        }
        
        #${CONFIG.uiElements.progressBarId} .bar {
            height: 100%;
            background-color: #2878ff;
            width: 0;
            transition: width 0.3s ease;
        }
        
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
        }
    `);

    // 创建导出按钮
    const createExportButton = () => {
        const button = document.createElement('button');
        button.id = CONFIG.uiElements.buttonId;
        button.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px;">
                <path d="M11.87 4.14a1 1 0 0 0-1.41 0L8.75 5.83V1a1 1 0 0 0-2 0v4.83L5.04 4.14a1 1 0 0 0-1.41 1.41l3.66 3.66c.2.2.45.29.7.29.25 0 .5-.1.7-.29l3.66-3.66a1 1 0 0 0 0-1.41z"/>
                <path d="M12.5 8.5a1 1 0 0 0-1 1V13H3V9.5a1 1 0 0 0-2 0V14a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V9.5a1 1 0 0 0-1-1z"/>
            </svg>
            导出PSD
        `;
        button.addEventListener('click', handleExportButtonClick);
        return button;
    };
    
    // 创建下载原图按钮
    const createDownloadImageButton = () => {
        const button = document.createElement('button');
        button.id = 'gd-original-image-download-button';
        button.innerHTML = `
             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px;">
                <path d="M4.502 9.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5z"/>
                <path d="M2 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2H2zm12 1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1h12z"/>
                <path d="M10.804 3.334a.5.5 0 0 1 .42.23l2.5 4a.5.5 0 0 1-.42.766h-5a.5.5 0 0 1-.42-.766l2.5-4a.5.5 0 0 1 .42-.23z"/>
            </svg>
            下载高清原图
        `;
        button.addEventListener('click', handleDownloadOriginalImageClick);
        return button;
    };

    // 创建模态框
    const createModal = () => {
        // 检查是否已存在
        if (document.getElementById(CONFIG.uiElements.modalId)) {
            return;
        }
        
        // 创建背景遮罩
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        document.body.appendChild(backdrop);
        
        // 创建模态框
        const modal = document.createElement('div');
        modal.id = CONFIG.uiElements.modalId;
        modal.innerHTML = `
            <h3>导出PSD文件</h3>
            <div class="content">
                <p>将导出当前设计为可编辑的PSD文件，保留图层和文字。</p>
                <div class="options">
                    <label>
                        <input type="checkbox" id="keep-layers" checked />
                        保持图层结构
                    </label>
                    <br />
                    <label>
                        <input type="checkbox" id="export-text-as-text" checked />
                        导出文字为可编辑文字层
                    </label>
                </div>
                <div id="${CONFIG.uiElements.progressBarId}" style="display: none;">
                    <div class="bar"></div>
                </div>
            </div>
            <div class="actions">
                <button class="cancel">取消</button>
                <button class="confirm">导出</button>
            </div>
        `;
        document.body.appendChild(modal);
        
        // 绑定事件
        modal.querySelector('button.cancel').addEventListener('click', hideModal);
        modal.querySelector('button.confirm').addEventListener('click', startExport);
        backdrop.addEventListener('click', hideModal);
        
        return modal;
    };

    // 显示模态框
    const showModal = () => {
        const modal = document.getElementById(CONFIG.uiElements.modalId) || createModal();
        modal.style.display = 'block';
        document.querySelector('.modal-backdrop').style.display = 'block';
    };

    // 隐藏模态框
    const hideModal = () => {
        const modal = document.getElementById(CONFIG.uiElements.modalId);
        if (modal) {
            modal.style.display = 'none';
        }
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.style.display = 'none';
        }
    };

    // 更新进度条
    const updateProgress = (percent) => {
        const progressBar = document.querySelector(`#${CONFIG.uiElements.progressBarId} .bar`);
        if (progressBar) {
            progressBar.style.width = `${percent}%`;
        }
    };

    // 显示进度条
    const showProgress = () => {
        const progressBar = document.getElementById(CONFIG.uiElements.progressBarId);
        if (progressBar) {
            progressBar.style.display = 'block';
            updateProgress(0);
        }
    };

    // 隐藏进度条
    const hideProgress = () => {
        const progressBar = document.getElementById(CONFIG.uiElements.progressBarId);
        if (progressBar) {
            progressBar.style.display = 'none';
        }
    };

    // ========================
    // 核心导出功能
    // ========================

    const extractFullDesignData = async () => {
        logger.log('开始采用【精确制导】多策略扫描获取设计数据...');
        
        const visited = new Set();

        function isElementLike(obj) {
            if (!obj || typeof obj !== 'object') return false;
            const hasType = typeof obj.type === 'string' && obj.type;
            const hasTransform = obj.hasOwnProperty('left') && obj.hasOwnProperty('top');
            const hasSize = obj.hasOwnProperty('width') && obj.hasOwnProperty('height');
            return hasType && hasTransform && hasSize;
        }

        function findDataRecursive(currentObj) {
            if (!currentObj || typeof currentObj !== 'object' || visited.has(currentObj)) {
                return null;
            }
            visited.add(currentObj);

            if (Array.isArray(currentObj.elements) && currentObj.elements.length > 0 &&
                typeof currentObj.width === 'number' && currentObj.width > 0 &&
                typeof currentObj.height === 'number' && currentObj.height > 0 &&
                isElementLike(currentObj.elements[0])) {

                const elements = currentObj.elements;
                if (elements.every(isElementLike)) {
                    logger.log('深度扫描匹配成功！父级对象:', currentObj);
                    return {
                        elements: elements,
                        width: currentObj.width,
                        height: currentObj.height,
                        background: currentObj.background // 增强: 同时提取背景信息
                    };
                }
            }

            for (const key in currentObj) {
                if (!currentObj.hasOwnProperty(key)) continue;
                if (['self', 'window', 'document', 'top', 'webpackChunk_N_E'].includes(key)) continue;

                try {
                    const prop = currentObj[key];
                    if (prop && typeof prop === 'object') {
                        const result = findDataRecursive(prop);
                        if (result) return result;
                    }
                } catch (e) { /* Ignore access errors */ }
            }
            return null;
        }
        
        function flattenElements(elements, parentLeft = 0, parentTop = 0) {
            let flatList = [];
            for (const el of elements) {
                const absoluteLeft = parentLeft + (el.left || 0);
                const absoluteTop = parentTop + (el.top || 0);

                if (el.type === 'group' && Array.isArray(el.elements)) {
                    flatList = flatList.concat(flattenElements(el.elements, absoluteLeft, absoluteTop));
                } else {
                    const id = el.id || `el-${Math.random().toString(36).substr(2, 9)}`;
                    const formattedEl = {
                        id: id,
                        name: el.name || `${el.type}-${id}`,
                        type: el.type,
                        visible: el.visible !== false,
                        left: absoluteLeft,
                        top: absoluteTop,
                        width: el.width,
                        height: el.height,
                        opacity: el.opacity,
                        rotation: el.transform ? el.transform.rotation : 0,
                        raw: el
                    };
                    flatList.push(formattedEl);
                }
            }
            return flatList;
        }

        // --- 最终方案 ---

        // 策略1: 精确打击用户指定的目标元素
        const targetSelectors = ['.editor-board', '.editor-stage'];
        for (const selector of targetSelectors) {
            const targetElement = document.querySelector(selector);
            if (targetElement) {
                logger.log(`找到目标容器元素: "${selector}"`, targetElement);
                for (const key in targetElement) {
                    if (key.startsWith('__')) { // 前端框架属性通常以'__'开头
                        logger.log(`检查目标元素的属性 [${key}]...`);
                        try {
                            const dataSource = targetElement[key];
                            if (dataSource && typeof dataSource === 'object') {
                                visited.clear(); // 为每次新的根搜索重置 visited
                                const found = findDataRecursive(dataSource);
                                if (found) {
                                    const flattened = flattenElements(found.elements);
                                    logger.log(`在 "${selector}" 的属性 [${key}] 中找到设计数据！`);
                                    return { ...found, elements: flattened }; // 增强: 传递包括背景在内的完整数据
                                }
                            }
                        } catch (e) { logger.warn(`检查属性 ${key} 时出错`, e) }
                    }
                }
            }
        }
        
        // 策略2: 全局扫描作为备用方案
        logger.warn('在指定元素上未找到数据，回退到全局扫描...');
        visited.clear();
        try {
            const foundGlobal = findDataRecursive(unsafeWindow);
            if (foundGlobal) {
                const flattened = flattenElements(foundGlobal.elements);
                logger.log(`全局扫描成功！`);
                return { ...foundGlobal, elements: flattened }; // 增强: 传递包括背景在内的完整数据
            }
        } catch (error) {
            logger.error('全局深度扫描时发生严重错误', error);
        }

        logger.error('【根本性失败】所有方案均未能找到任何有效的设计数据。无法进行分层导出。');
        return null;
    };

    // 使用浏览器代理下载图片并返回ArrayBuffer
    const fetchImageArrayBuffer = (url) => {
        return new Promise((resolve, reject) => {
            if (!url.startsWith('http')) { url = 'https:' + url; }

            const img = new Image();
            img.crossOrigin = "anonymous"; // 尝试请求CORs权限
            img.onload = () => {
                const canvas = document.createElement('canvas');
                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                const ctx = canvas.getContext('2d');
                if (!ctx) return reject(new Error("无法为图片代理创建2D上下文"));
                ctx.drawImage(img, 0, 0);
                canvas.toBlob(blob => {
                     if (!blob) return reject(new Error("无法从Canvas创建Blob"));
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = () => reject(new Error("FileReader读取Blob时出错"));
                    reader.readAsArrayBuffer(blob);
                });
            };
            img.onerror = () => reject(new Error(`通过Image标签加载图片失败: ${url}`));
            img.src = url;
        });
    };

    const renderSvgToBuffer = (svgString, width, height) => {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);
                canvas.toBlob(blob => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsArrayBuffer(blob);
                }, 'image/png');
            };
            img.onerror = (e) => reject(new Error("SVG 加载到 Image 时出错", { cause: e }));
            // 修复SVG中的特殊字符
            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgString)));
        });
    }

    // 创建PSD文件数据
    const createPSDFile = async (designData, layers) => {
        try {
            logger.log('开始使用 ag-psd 创建PSD文件', { layersCount: layers.length });
            showProgress();

            const psd = {
                width: designData.width,
                height: designData.height,
                children: []
            };
            updateProgress(10);

            const reversedLayers = [...layers].reverse();

            for (const [index, layer] of reversedLayers.entries()) {
                const progress = 10 + Math.floor(((index + 1) / reversedLayers.length) * 80);
                updateProgress(progress);

                if (!layer.visible) {
                    logger.log(`跳过不可见图层: "${layer.name}"`);
                    continue;
                }
                
                // 根源性修复: 将 0-1 的 opacity 转换为 0-255
                const layerOpacity = Math.round((typeof layer.opacity === 'number' ? layer.opacity : 1) * 255);

                if (layer.type === 'image' && layer.raw.url) {
                    try {
                        logger.log(`处理图片图层: "${layer.name}"`, layer.raw.url);
                        const imageBuffer = await fetchImageArrayBuffer(layer.raw.url);
                        psd.children.push({
                            name: layer.name, left: layer.left, top: layer.top,
                            width: layer.width, height: layer.height, opacity: layerOpacity,
                            image: imageBuffer
                        });
                    } catch (e) {
                        logger.error(`处理图片图层 "${layer.name}" 失败`, e);
                    }
                } else if (layer.type === 'svg') {
                 const svgContent = layer.raw.svg || layer.raw.content;
                 if (svgContent) {
                    try {
                        logger.log(`处理SVG图层: "${layer.name}"`);
                        const imageBuffer = await renderSvgToBuffer(svgContent, layer.width, layer.height);
                        psd.children.push({
                            name: layer.name, left: layer.left, top: layer.top,
                            width: layer.width, height: layer.height, opacity: layerOpacity,
                            image: imageBuffer
                        });
                    } catch(e) {
                        logger.error(`处理SVG图层 "${layer.name}" 失败`, e);
                    }
                } else {
                    logger.warn(`跳过无内容的SVG图层: "${layer.name}"`);
                }
            } else if (layer.type === 'mask') {
                logger.log(`跳过蒙版图层(暂不支持): "${layer.name}"`);
            } else if (layer.type === 'text') {
                logger.log(`处理文本图层: "${layer.name}"`);
                const text = layer.raw;
                const font = text.fontStyle || {};
                
                let r = 0, g = 0, b = 0, a = 255; // 默认不透明黑色

                const mainFill = Array.isArray(text.fills) ? text.fills.find(f => f.isEnabled !== false) : null;

                if (mainFill && mainFill.color && mainFill.color.rgba) {
                    const c = mainFill.color.rgba;
                    r = c.r || 0;
                    g = c.g || 0;
                    b = c.b || 0;
                    a = typeof c.a === 'number' ? Math.round(c.a * 255) : 255;
                } else if (text.color) { // Fallback to old text.color property
                    if (text.color.rgba) {
                        const c = text.color.rgba;
                        r = c.r || 0;
                        g = c.g || 0;
                        b = c.b || 0;
                        a = typeof c.a === 'number' ? Math.round(c.a * 255) : 255;
                    } else if (typeof text.color === 'string') {
                        const hexMatch = text.color.match(/#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})/i);
                         if (hexMatch) {
                            r = parseInt(hexMatch[1], 16);
                            g = parseInt(hexMatch[2], 16);
                            b = parseInt(hexMatch[3], 16);
                            a = 255;
                        }
                    }
                }

                psd.children.push({
                    name: layer.name, left: layer.left, top: layer.top, opacity: layerOpacity,
                    text: {
                        text: text.content || '',
                        font: {
                            name: font.fontFamily || 'SourceHanSansSC',
                            size: font.fontSize || 16,
                            color: { r, g, b, a },
                            alignment: text.textAlign ? ['left', 'center', 'right'].indexOf(text.textAlign) : 0
                        },
                    },
                });
            } else {
                logger.log(`跳过不支持或无内容的图层类型: "${layer.type}" - "${layer.name}"`);
            }
        }

        updateProgress(95);

        if (psd.children.length === 0) {
            logger.warn('未能提取或处理任何图层，将生成一个空的PSD。');
        }

        logger.log('准备写入PSD数据...', psd);
        const buffer = agPsd.writePsd(psd);
        updateProgress(100);

        return new Blob([buffer], { type: 'application/vnd.adobe.photoshop' });

    } catch (error) {
        logger.error('使用 ag-psd 创建PSD文件失败', error);
        hideProgress();
        throw error;
    }
};

    // ========================
    // 新增：图像加载工具函数
    // ========================
    const loadImage = (url) => {
        return new Promise((resolve, reject) => {
            if (!url) return reject(new Error('Image URL is empty.'));
            if (!url.startsWith('http')) { url = 'https:' + url; }

            const img = new Image();
            img.crossOrigin = "anonymous";
            img.onload = () => resolve(img);
            img.onerror = () => {
                logger.warn(`常规Image加载失败: ${url}, 尝试GM_xmlhttpRequest代理...`);
                // 备用方案: 使用GM_xmlhttpRequest获取blob，然后创建URL
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: url,
                    responseType: 'blob',
                    headers: {
                        'Referer': 'https://www.gaoding.com/' // 伪装来源
                    },
                    onload: function(response) {
                        const blobUrl = URL.createObjectURL(response.response);
                        const proxiedImg = new Image();
                        proxiedImg.onload = () => {
                            URL.revokeObjectURL(blobUrl);
                            resolve(proxiedImg);
                        };
                        proxiedImg.onerror = () => {
                             URL.revokeObjectURL(blobUrl);
                             reject(new Error(`代理Image加载也失败: ${url}`));
                        }
                        proxiedImg.src = blobUrl;
                    },
                    onerror: function(error) {
                        reject(new Error(`GM_xmlhttpRequest failed for image: ${url}`, {cause: error}));
                    }
                });
            };
            img.src = url;
        });
    };

    const loadSvgAsImage = (svgString) => {
        return new Promise((resolve, reject) => {
            if (!svgString) return reject(new Error('SVG content is empty.'));
            const img = new Image();
            img.onload = () => resolve(img);
img.onerror = (e) => reject(new Error("SVG加载到Image失败", { cause: e }));
            // 修复SVG中的特殊字符
            const sanitizedSvg = unescape(encodeURIComponent(svgString));
            img.src = 'data:image/svg+xml;base64,' + btoa(sanitizedSvg);
        });
    };

    // ========================
    // 事件处理
    // ========================
    
    // 导出按钮点击处理
    const handleExportButtonClick = (event) => {
        event.preventDefault();
        showModal();
    };

    // 下载原图按钮点击处理
    const handleDownloadOriginalImageClick = async () => {
        logger.info('开始下载高清原图...');
        const downloadButton = document.getElementById('gd-original-image-download-button');
        const originalButtonContent = downloadButton.innerHTML;
        if (downloadButton) {
            downloadButton.innerHTML = '生成中...';
            downloadButton.disabled = true;
        }

        try {
            // 步骤1：智能查找设计容器 - 多重策略
            let designContainer = null;
            let designWidth = 0, designHeight = 0;
            
            // 策略A: 查找设计区域相关的尺寸信息
            const sizeInfoElements = Array.from(document.querySelectorAll('.editor-size-info, .size-info, [class*="size-info"], [class*="canvas-size"], [class*="design-size"]'));
            let sizeFromUI = null;
            for (const el of sizeInfoElements) {
                const text = el.textContent;
                const match = text.match(/(\d+)\s*[×x]\s*(\d+)/);
                if (match) {
                    sizeFromUI = {
                        width: parseInt(match[1], 10),
                        height: parseInt(match[2], 10)
                    };
                    logger.log(`从UI获取到设计尺寸: ${sizeFromUI.width}x${sizeFromUI.height}`);
                    break;
                }
            }
            
            // 策略B: 尝试从数据中获取尺寸
            let sizeFromData = null;
            try {
                const designData = await extractFullDesignData();
                if (designData && designData.width && designData.height) {
                    sizeFromData = {
                        width: designData.width,
                        height: designData.height
                    };
                    logger.log(`从数据中获取到设计尺寸: ${sizeFromData.width}x${sizeFromData.height}`);
                }
            } catch (e) {
                logger.warn('从数据中获取设计尺寸失败', e);
            }
            
            // 策略C: 尝试从URL参数或页面元素获取尺寸
            let sizeFromURL = null;
            try {
                // 尝试从URL中提取尺寸信息
                const urlParams = new URLSearchParams(window.location.search);
                const sizeParam = urlParams.get('size') || urlParams.get('dimensions');
                if (sizeParam) {
                    const match = sizeParam.match(/(\d+)[^\d]+(\d+)/);
                    if (match) {
                        sizeFromURL = {
                            width: parseInt(match[1], 10),
                            height: parseInt(match[2], 10)
                        };
                        logger.log(`从URL获取到设计尺寸: ${sizeFromURL.width}x${sizeFromURL.height}`);
                    }
                }
                
                // 尝试从页面元素属性中获取尺寸
                if (!sizeFromURL) {
                    const canvasElements = document.querySelectorAll('canvas');
                    for (const canvas of canvasElements) {
                        if (canvas.width > 300 && canvas.height > 300) {
                            const dataWidth = canvas.getAttribute('data-width') || canvas.getAttribute('data-original-width');
                            const dataHeight = canvas.getAttribute('data-height') || canvas.getAttribute('data-original-height');
                            
                            if (dataWidth && dataHeight) {
                                sizeFromURL = {
                                    width: parseInt(dataWidth, 10),
                                    height: parseInt(dataHeight, 10)
                                };
                                logger.log(`从Canvas属性获取到设计尺寸: ${sizeFromURL.width}x${sizeFromURL.height}`);
                                break;
                            }
                        }
                    }
                }
            } catch (e) {
                logger.warn('从URL或元素属性获取设计尺寸失败', e);
            }

            // 确定最终使用的尺寸 (优先级: UI > URL > 数据 > 容器)
            if (sizeFromUI && sizeFromUI.width > 0 && sizeFromUI.height > 0) {
                designWidth = sizeFromUI.width;
                designHeight = sizeFromUI.height;
                logger.log('使用从UI获取的尺寸');
            } else if (sizeFromURL && sizeFromURL.width > 0 && sizeFromURL.height > 0) {
                designWidth = sizeFromURL.width;
                designHeight = sizeFromURL.height;
                logger.log('使用从URL或元素属性获取的尺寸');
            } else if (sizeFromData && sizeFromData.width > 0 && sizeFromData.height > 0) {
                designWidth = sizeFromData.width;
                designHeight = sizeFromData.height;
                logger.log('使用从数据获取的尺寸');
            } else {
                const rect = designContainer.getBoundingClientRect();
                designWidth = rect.width;
                designHeight = rect.height;
                logger.log('使用容器尺寸');
            }
            
            // 尺寸校正：确保尺寸不会太小
            const minWidth = 1200; // 最小宽度
            const minHeight = 1800; // 最小高度
            
            if (designWidth < minWidth || designHeight < minHeight) {
                // 如果尺寸太小，按比例放大
                const ratio = designWidth / designHeight;
                if (ratio >= 1) { // 横向或正方形
                    designWidth = Math.max(designWidth, minWidth);
                    designHeight = designWidth / ratio;
                } else { // 纵向
                    designHeight = Math.max(designHeight, minHeight);
                    designWidth = designHeight * ratio;
                }
                logger.log(`尺寸太小，已调整为: ${designWidth}x${designHeight}`);
            }
            
            logger.log(`最终确定的设计尺寸: ${designWidth}x${designHeight}`);
            
            // 步骤2：准备捕获设置
            // 增加缩放比例，确保输出图像足够大且清晰
            const scale = Math.min(Math.max(8, window.devicePixelRatio * 4 || 8), 12);
            logger.log(`使用超高缩放比例: ${scale}，目标输出尺寸: ${designWidth * scale}x${designHeight * scale}`);

            // 步骤3：执行捕获 - 使用多种备用策略
            let finalCanvas = null;
            
            // 策略1: 使用html2canvas直接捕获
            try {
                logger.log('尝试使用html2canvas捕获...');
                
                // 准备捕获前的处理
                const originalOverflow = document.body.style.overflow;
                document.body.style.overflow = 'visible'; // 确保内容不被裁切
                
                // 优化：先尝试使用精确尺寸
                const canvas = await html2canvas(designContainer, {
                    scale: scale,
                    useCORS: true,
                    allowTaint: true,
                    logging: false,
                    backgroundColor: null,
                    width: designWidth,
                    height: designHeight,
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: designWidth,
                    windowHeight: designHeight,
                    foreignObjectRendering: true,
                    removeContainer: false,
                    ignoreElements: (element) => {
                        // 忽略可能干扰的UI元素
                        return element.classList && (
                            element.classList.contains('editor-toolbar') ||
                            element.classList.contains('editor-panel') ||
                            element.classList.contains('editor-sidebar') ||
                            element.classList.contains('modal') ||
                            element.classList.contains('popup')
                        );
                    },
                    onclone: (clonedDoc) => {
                        // 在克隆的文档中处理元素
                        const clonedContainer = clonedDoc.querySelector('.editor-board, .editor-stage, .editor-design-board, .canvas-container, .editor-content, .design-content');
                        if (clonedContainer) {
                            // 移除可能干扰的元素
                            const elementsToRemove = clonedContainer.querySelectorAll('.editor-toolbar, .editor-panel, .editor-sidebar, .modal, .popup');
                            elementsToRemove.forEach(el => el.remove());
                            
                            // 确保内容可见且不被裁切
                            clonedContainer.style.overflow = 'visible';
                            clonedContainer.style.maxHeight = 'none';
                            clonedContainer.style.maxWidth = 'none';
                            clonedContainer.style.height = designHeight + 'px';
                            clonedContainer.style.width = designWidth + 'px';
                        }
                    }
                });
                
                // 恢复原始状态
                document.body.style.overflow = originalOverflow;
                
                // 检查捕获结果是否有效
                const hasContent = checkCanvasHasContent(canvas);
                if (hasContent) {
                    finalCanvas = canvas;
                    logger.log('html2canvas捕获成功!');
                } else {
                    logger.warn('html2canvas捕获的内容为空，尝试备用方案...');
                }
            } catch (e) {
                logger.error('html2canvas捕获失败，尝试备用方案...', e);
            }
            
            // 策略2: 如果html2canvas失败，尝试直接使用canvas元素
            if (!finalCanvas) {
                logger.log('尝试使用页面上的canvas元素...');
                const canvasElements = Array.from(document.querySelectorAll('canvas'))
                    .filter(c => c.width > 100 && c.height > 100)
                    .sort((a, b) => (b.width * b.height) - (a.width * a.height));
                
                if (canvasElements.length > 0) {
                    const sourceCanvas = canvasElements[0];
                    
                    // 创建新画布，使用精确的设计尺寸
                    finalCanvas = document.createElement('canvas');
                    finalCanvas.width = designWidth * scale;
                    finalCanvas.height = designHeight * scale;
                    
                    const ctx = finalCanvas.getContext('2d');
                    if (ctx) {
                        // 使用高质量缩放
                        ctx.imageSmoothingEnabled = true;
                        ctx.imageSmoothingQuality = 'high';
                        
                        // 清除背景并填充白色
                        ctx.fillStyle = '#FFFFFF';
                        ctx.fillRect(0, 0, finalCanvas.width, finalCanvas.height);
                        
                        // 计算绘制参数 - 使用COVER模式确保铺满
                        const sourceRatio = sourceCanvas.width / sourceCanvas.height;
                        const targetRatio = finalCanvas.width / finalCanvas.height;
                        
                        let sx = 0, sy = 0, sWidth = sourceCanvas.width, sHeight = sourceCanvas.height;
                        
                        if (sourceRatio > targetRatio) {
                            // 源更宽，裁剪宽度
                            sWidth = sourceCanvas.height * targetRatio;
                            sx = (sourceCanvas.width - sWidth) / 2;
                        } else {
                            // 源更高，裁剪高度
                            sHeight = sourceCanvas.width / targetRatio;
                            sy = (sourceCanvas.height - sHeight) / 2;
                        }
                        
                        // 绘制内容 - 铺满整个画布
                        ctx.drawImage(sourceCanvas, 
                                     sx, sy, sWidth, sHeight, 
                                     0, 0, finalCanvas.width, finalCanvas.height);
                        
                        // 应用锐化处理以提高清晰度
                        try {
                            applyImageSharpening(ctx, finalCanvas.width, finalCanvas.height);
                        } catch (e) {
                            logger.warn('应用锐化处理失败', e);
                        }
                        
                        const hasContent = checkCanvasHasContent(finalCanvas);
                        if (hasContent) {
                            logger.log(`使用页面canvas元素成功! 输出尺寸: ${finalCanvas.width}x${finalCanvas.height}`);
                        } else {
                            finalCanvas = null;
                            logger.warn('页面canvas元素内容为空，尝试下一个备用方案...');
                        }
                    }
                }
            }
            
            // 策略3: 如果前两种方法都失败，尝试使用截图API (如果浏览器支持)
            if (!finalCanvas && navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                try {
                    logger.log('尝试使用屏幕捕获API...');
                    const stream = await navigator.mediaDevices.getDisplayMedia({
                        video: {
                            displaySurface: 'window'
                        }
                    });
                    
                    // 创建视频元素
                    const video = document.createElement('video');
                    video.srcObject = stream;
                    
                    // 等待视频加载
                    await new Promise(resolve => {
                        video.onloadedmetadata = resolve;
                        video.play();
                    });
                    
                    // 创建画布并捕获视频帧
                    finalCanvas = document.createElement('canvas');
                    finalCanvas.width = video.videoWidth;
                    finalCanvas.height = video.videoHeight;
                    
                    const ctx = finalCanvas.getContext('2d');
                    ctx.drawImage(video, 0, 0);
                    
                    // 停止流
                    stream.getTracks().forEach(track => track.stop());
                    
                    logger.log('屏幕捕获API成功!');
                } catch (e) {
                    logger.error('屏幕捕获API失败', e);
                }
            }
            
            // 如果所有策略都失败，返回错误
            if (!finalCanvas) {
                throw new Error('所有捕获方法都失败，无法获取设计图像。');
            }
            
            // 最终尺寸校验和调整
            if (finalCanvas.width !== designWidth * scale || finalCanvas.height !== designHeight * scale) {
                logger.warn(`最终画布尺寸(${finalCanvas.width}x${finalCanvas.height})与目标尺寸(${designWidth * scale}x${designHeight * scale})不符，进行调整...`);
                
                const correctCanvas = document.createElement('canvas');
                correctCanvas.width = designWidth * scale;
                correctCanvas.height = designHeight * scale;
                
                const ctx = correctCanvas.getContext('2d');
                // 使用cover模式绘制，确保铺满
                ctx.fillStyle = '#FFFFFF';
                ctx.fillRect(0, 0, correctCanvas.width, correctCanvas.height);
                
                // 使用高质量绘制设置
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';
                
                const sourceRatio = finalCanvas.width / finalCanvas.height;
                const targetRatio = correctCanvas.width / correctCanvas.height;
                
                let sx = 0, sy = 0, sWidth = finalCanvas.width, sHeight = finalCanvas.height;
                
                if (sourceRatio > targetRatio) {
                    // 源更宽，裁剪宽度
                    sWidth = finalCanvas.height * targetRatio;
                    sx = (finalCanvas.width - sWidth) / 2;
                } else {
                    // 源更高，裁剪高度
                    sHeight = finalCanvas.width / targetRatio;
                    sy = (finalCanvas.height - sHeight) / 2;
                }
                
                ctx.drawImage(finalCanvas, 
                             sx, sy, sWidth, sHeight, 
                             0, 0, correctCanvas.width, correctCanvas.height);
                
                // 应用锐化处理以提高清晰度
                try {
                    applyImageSharpening(ctx, correctCanvas.width, correctCanvas.height);
                } catch (e) {
                    logger.warn('应用锐化处理失败', e);
                }
                             
                finalCanvas = correctCanvas;
                logger.log(`尺寸校正完成，最终尺寸: ${finalCanvas.width}x${finalCanvas.height}`);
            }
            
            // 步骤4：导出为PNG - 使用最高质量
            finalCanvas.toBlob(function(blob) {
                if (!blob) {
                    alert(`下载原图失败: 无法从画布创建图像数据。`);
                    return;
                }
                const filename = `${CONFIG.defaultPsdName}_超清大图_${new Date().getTime()}.png`;
                saveAs(blob, filename);
                logger.info(`超清原图 "${filename}" 已成功触发下载！尺寸: ${finalCanvas.width}x${finalCanvas.height}`);
                // 删除烦人的确认弹窗
                // alert(`高清原图 "${filename}" 已开始下载！`);
            }, 'image/png', 1.0);

        } catch (error) {
            logger.error('下载高清原图失败', error);
            alert(`下载原图失败: ${error.message}`);
        } finally {
            if (downloadButton) {
                downloadButton.innerHTML = originalButtonContent;
                downloadButton.disabled = false;
            }
        }
    };
    
    // 辅助函数：应用锐化滤镜以提高图像清晰度
    function applyImageSharpening(ctx, width, height) {
        // 获取图像数据
        const imageData = ctx.getImageData(0, 0, width, height);
        const data = imageData.data;
        const dataWidth = width;
        const dataHeight = height;
        
        // 创建临时数组存储处理后的数据
        const resultData = new Uint8ClampedArray(data.length);
        
        // 锐化卷积核
        const kernel = [
            0, -1, 0,
            -1, 5, -1,
            0, -1, 0
        ];
        
        // 应用卷积核
        for (let y = 1; y < dataHeight - 1; y++) {
            for (let x = 1; x < dataWidth - 1; x++) {
                const offset = (y * dataWidth + x) * 4;
                
                // 对RGB通道应用锐化
                for (let c = 0; c < 3; c++) {
                    let val = 0;
                    
                    // 应用3x3卷积核
                    val += data[((y - 1) * dataWidth + (x - 1)) * 4 + c] * kernel[0];
                    val += data[((y - 1) * dataWidth + x) * 4 + c] * kernel[1];
                    val += data[((y - 1) * dataWidth + (x + 1)) * 4 + c] * kernel[2];
                    val += data[(y * dataWidth + (x - 1)) * 4 + c] * kernel[3];
                    val += data[(y * dataWidth + x) * 4 + c] * kernel[4];
                    val += data[(y * dataWidth + (x + 1)) * 4 + c] * kernel[5];
                    val += data[((y + 1) * dataWidth + (x - 1)) * 4 + c] * kernel[6];
                    val += data[((y + 1) * dataWidth + x) * 4 + c] * kernel[7];
                    val += data[((y + 1) * dataWidth + (x + 1)) * 4 + c] * kernel[8];
                    
                    // 限制值在0-255范围内
                    resultData[offset + c] = Math.min(255, Math.max(0, val));
                }
                
                // 保持Alpha通道不变
                resultData[offset + 3] = data[offset + 3];
            }
        }
        
        // 复制边缘像素（不应用卷积）
        for (let y = 0; y < dataHeight; y++) {
            for (let x = 0; x < dataWidth; x++) {
                if (x === 0 || x === dataWidth - 1 || y === 0 || y === dataHeight - 1) {
                    const offset = (y * dataWidth + x) * 4;
                    resultData[offset] = data[offset];
                    resultData[offset + 1] = data[offset + 1];
                    resultData[offset + 2] = data[offset + 2];
                    resultData[offset + 3] = data[offset + 3];
                }
            }
        }
        
        // 创建新的ImageData并应用到画布
        const resultImageData = new ImageData(resultData, width, height);
        ctx.putImageData(resultImageData, 0, 0);
    }

    // 辅助函数：检查画布是否有内容（非全透明）
    function checkCanvasHasContent(canvas) {
        try {
            const ctx = canvas.getContext('2d');
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            
            // 检查是否有非透明像素
            for (let i = 3; i < data.length; i += 4) {
                if (data[i] > 0) return true;
            }
            
            return false;
        } catch (e) {
            logger.error('检查画布内容时出错', e);
            return true; // 出错时假定有内容
        }
    }

    // 开始导出流程
    const startExport = async () => {
        try {
            // 更新UI状态
            const exportButton = document.getElementById(CONFIG.uiElements.buttonId);
            if (exportButton) {
                exportButton.classList.add('exporting');
                exportButton.innerHTML = '导出中...';
            }
            
            // 显示进度条
            showProgress();
            updateProgress(5);
            
            logger.log('开始深度扫描以提取设计数据...');
            const designData = await extractFullDesignData();

            if (!designData || designData.elements.length === 0) {
                alert('导出失败：无法从页面中提取到有效的设计数据。请刷新页面或联系脚本作者。');
                hideModal();
                // 在这里重置按钮状态
                if (exportButton) {
                    exportButton.classList.remove('exporting');
                    exportButton.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px;">
                            <path d="M11.87 4.14a1 1 0 0 0-1.41 0L8.75 5.83V1a1 1 0 0 0-2 0v4.83L5.04 4.14a1 1 0 0 0-1.41 1.41l3.66 3.66c.2.2.45.29.7.29.25 0 .5-.1.7-.29l3.66-3.66a1 1 0 0 0 0-1.41z"/>
                            <path d="M12.5 8.5a1 1 0 0 0-1 1V13H3V9.5a1 1 0 0 0-2 0V14a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V9.5a1 1 0 0 0-1-1z"/>
                        </svg>
                        导出PSD
                    `;
                }
                return;
            }
            updateProgress(30);
            
            // 创建PSD文件
            logger.log('创建PSD文件...');
            const psdData = await createPSDFile(designData, designData.elements);
            
            // 保存PSD文件
            const filename = `${CONFIG.defaultPsdName}_${new Date().getTime()}.psd`;
            saveAs(psdData, filename);
            
            // 完成处理
            logger.info('PSD文件导出成功!', filename);
            hideModal();
            
            // 重置UI
            if (exportButton) {
                exportButton.classList.remove('exporting');
                exportButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px;">
                        <path d="M11.87 4.14a1 1 0 0 0-1.41 0L8.75 5.83V1a1 1 0 0 0-2 0v4.83L5.04 4.14a1 1 0 0 0-1.41 1.41l3.66 3.66c.2.2.45.29.7.29.25 0 .5-.1.7-.29l3.66-3.66a1 1 0 0 0 0-1.41z"/>
                        <path d="M12.5 8.5a1 1 0 0 0-1 1V13H3V9.5a1 1 0 0 0-2 0V14a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V9.5a1 1 0 0 0-1-1z"/>
                    </svg>
                    导出PSD
                `;
            }
            
            // 删除烦人的确认弹窗
            // 显示成功消息
            // alert(`PSD文件 "${filename}" 已成功导出！`);
            
        } catch (error) {
            logger.error('导出PSD文件失败', error);
            alert(`导出失败: ${error.message}`);
            
            // 重置UI
            const exportButton = document.getElementById(CONFIG.uiElements.buttonId);
            if (exportButton) {
                exportButton.classList.remove('exporting');
                exportButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px;">
                        <path d="M11.87 4.14a1 1 0 0 0-1.41 0L8.75 5.83V1a1 1 0 0 0-2 0v4.83L5.04 4.14a1 1 0 0 0-1.41 1.41l3.66 3.66c.2.2.45.29.7.29.25 0 .5-.1.7-.29l3.66-3.66a1 1 0 0 0 0-1.41z"/>
                        <path d="M12.5 8.5a1 1 0 0 0-1 1V13H3V9.5a1 1 0 0 0-2 0V14a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V9.5a1 1 0 0 0-1-1z"/>
                    </svg>
                    导出PSD
                `;
            }
            
            hideModal();
        }
    };

    // ========================
    // 初始化和安装
    // ========================
    
    // 插入导出按钮
    const insertButtons = () => {
        // 检查按钮是否已存在
        if (document.getElementById(CONFIG.uiElements.buttonId)) {
            return;
        }
        
        // 创建并插入按钮到 body
        const exportButton = createExportButton();
        document.body.appendChild(exportButton);
        logger.log('导出按钮已作为悬浮按钮添加到页面');
        
        const downloadImageButton = createDownloadImageButton();
        document.body.appendChild(downloadImageButton);
        logger.log('下载原图按钮已添加到页面');
    };
    
    // 脚本初始化
    const init = () => {
        logger.info('稿定设计PSD导出工具初始化');
        
        // 检查依赖库
        if (typeof agPsd === 'undefined' || typeof saveAs === 'undefined' || typeof html2canvas === 'undefined') {
            logger.error('依赖库未加载，脚本无法运行');
            alert('稿定设计PSD导出工具需要的依赖库 ag-psd 或 FileSaver 或 html2canvas 未加载，请检查脚本设置或刷新页面重试');
            return;
        }

        // 页面加载完成后插入按钮
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            insertButtons();
        } else {
            window.addEventListener('load', insertButtons);
        }
    };
    
    // 启动脚本
    init();
})(); 