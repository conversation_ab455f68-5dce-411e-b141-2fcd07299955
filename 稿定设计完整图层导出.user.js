// ==UserScript==
// @name         稿定设计完整图层导出工具
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  完整导出稿定设计的所有图层和素材，确保每个元素都能独立编辑
// <AUTHOR>
// @match        https://www.gaoding.com/editor/*
// @match        https://gaoding.com/editor/*
// @grant        GM_xmlhttpRequest
// @grant        none
// @require      https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
// @require      https://unpkg.com/ag-psd@14.3.11/dist/bundle.js
// ==/UserScript==

(function() {
    'use strict';

    let isProcessing = false;

    // 等待库加载
    function waitForLibraries() {
        return new Promise((resolve) => {
            function check() {
                if (window.html2canvas && window.agPsd) {
                    console.log('✅ 库加载完成');
                    resolve();
                } else {
                    setTimeout(check, 500);
                }
            }
            check();
        });
    }

    // 完整图层导出器
    class CompleteLayerExporter {
        constructor() {
            this.layers = [];
            this.width = 800;
            this.height = 600;
            this.designData = null;
        }

        // 深度扫描获取设计数据
        async extractDesignData() {
            console.log('🔍 开始深度扫描设计数据...');
            
            // 多种策略获取设计数据
            const strategies = [
                () => this.findInReactFiber(),
                () => this.findInVueData(),
                () => this.findInGlobalObjects(),
                () => this.findInDOMAttributes()
            ];

            for (const strategy of strategies) {
                try {
                    const data = await strategy();
                    if (data && data.elements && data.elements.length > 0) {
                        console.log('✅ 找到设计数据:', data);
                        return data;
                    }
                } catch (e) {
                    console.warn('策略失败:', e.message);
                }
            }

            throw new Error('无法获取设计数据');
        }

        // React Fiber 数据提取
        findInReactFiber() {
            const containers = document.querySelectorAll('[class*="editor"], [class*="canvas"], [class*="design"]');
            
            for (const container of containers) {
                for (const key in container) {
                    if (key.startsWith('__reactInternalInstance') || key.startsWith('_reactInternalFiber')) {
                        const fiber = container[key];
                        const data = this.traverseFiber(fiber);
                        if (data) return data;
                    }
                }
            }
            return null;
        }

        // 遍历 React Fiber
        traverseFiber(fiber, depth = 0) {
            if (!fiber || depth > 10) return null;

            // 检查当前节点的props和state
            if (fiber.memoizedProps || fiber.memoizedState) {
                const props = fiber.memoizedProps || {};
                const state = fiber.memoizedState || {};
                
                // 查找设计数据
                const candidates = [props, state, props.value, state.value];
                for (const candidate of candidates) {
                    if (this.isValidDesignData(candidate)) {
                        return candidate;
                    }
                }
            }

            // 递归检查子节点
            if (fiber.child) {
                const result = this.traverseFiber(fiber.child, depth + 1);
                if (result) return result;
            }

            // 检查兄弟节点
            if (fiber.sibling) {
                const result = this.traverseFiber(fiber.sibling, depth + 1);
                if (result) return result;
            }

            return null;
        }

        // 检查是否为有效的设计数据
        isValidDesignData(obj) {
            return obj && 
                   typeof obj === 'object' && 
                   Array.isArray(obj.elements) && 
                   obj.elements.length > 0 &&
                   typeof obj.width === 'number' && 
                   typeof obj.height === 'number' &&
                   obj.elements.some(el => el.type && (el.left !== undefined || el.x !== undefined));
        }

        // Vue数据提取
        findInVueData() {
            const vueElements = document.querySelectorAll('[data-v-]');
            for (const el of vueElements) {
                if (el.__vue__) {
                    const data = this.searchVueData(el.__vue__);
                    if (data) return data;
                }
            }
            return null;
        }

        searchVueData(vue) {
            if (!vue) return null;
            
            const candidates = [vue.$data, vue.$props, vue.data, vue.props];
            for (const candidate of candidates) {
                if (this.isValidDesignData(candidate)) {
                    return candidate;
                }
            }

            // 递归搜索子组件
            if (vue.$children) {
                for (const child of vue.$children) {
                    const result = this.searchVueData(child);
                    if (result) return result;
                }
            }

            return null;
        }

        // 全局对象搜索
        findInGlobalObjects() {
            const globalKeys = ['__INITIAL_STATE__', '__PRELOADED_STATE__', 'window.store', 'window.app'];
            
            for (const key of globalKeys) {
                try {
                    const obj = eval(key);
                    if (obj) {
                        const data = this.deepSearch(obj);
                        if (data) return data;
                    }
                } catch (e) {
                    // 忽略错误
                }
            }

            // 搜索window对象
            return this.deepSearch(window, 3);
        }

        // 深度搜索对象
        deepSearch(obj, maxDepth = 5, currentDepth = 0, visited = new Set()) {
            if (!obj || typeof obj !== 'object' || currentDepth >= maxDepth || visited.has(obj)) {
                return null;
            }
            
            visited.add(obj);

            if (this.isValidDesignData(obj)) {
                return obj;
            }

            for (const key in obj) {
                if (key.startsWith('_') || key === 'constructor') continue;
                
                try {
                    const value = obj[key];
                    if (value && typeof value === 'object') {
                        const result = this.deepSearch(value, maxDepth, currentDepth + 1, visited);
                        if (result) return result;
                    }
                } catch (e) {
                    // 忽略访问错误
                }
            }

            return null;
        }

        // DOM属性搜索
        findInDOMAttributes() {
            const elements = document.querySelectorAll('*');
            
            for (const el of elements) {
                // 检查data属性
                for (const attr of el.attributes) {
                    if (attr.name.startsWith('data-') && attr.value) {
                        try {
                            const data = JSON.parse(attr.value);
                            if (this.isValidDesignData(data)) {
                                return data;
                            }
                        } catch (e) {
                            // 忽略JSON解析错误
                        }
                    }
                }
            }

            return null;
        }

        // 处理单个图层
        async processLayer(element, index, canvasRect) {
            try {
                console.log(`🎨 处理图层 ${index + 1}: ${element.type} - ${element.name || 'unnamed'}`);

                const layer = {
                    name: element.name || `${element.type}_${index + 1}`,
                    type: element.type,
                    left: element.left || element.x || 0,
                    top: element.top || element.y || 0,
                    width: element.width || 100,
                    height: element.height || 100,
                    opacity: Math.round((element.opacity || 1) * 255),
                    visible: element.visible !== false,
                    raw: element
                };

                // 根据类型处理不同的图层
                if (element.type === 'image' && element.url) {
                    layer.canvas = await this.createImageCanvas(element);
                } else if (element.type === 'text') {
                    layer.canvas = await this.createTextCanvas(element);
                } else if (element.type === 'shape') {
                    layer.canvas = await this.createShapeCanvas(element);
                } else if (element.type === 'svg') {
                    layer.canvas = await this.createSVGCanvas(element);
                } else {
                    // 通用处理：尝试从DOM中找到对应元素
                    layer.canvas = await this.createGenericCanvas(element);
                }

                if (layer.canvas && layer.canvas.width > 0 && layer.canvas.height > 0) {
                    this.layers.push(layer);
                    console.log(`✅ 图层 ${layer.name} 处理成功`);
                    return true;
                } else {
                    console.warn(`⚠️ 图层 ${layer.name} 无有效内容`);
                    return false;
                }

            } catch (error) {
                console.error(`❌ 处理图层 ${index + 1} 失败:`, error);
                return false;
            }
        }

        // 创建图片画布
        async createImageCanvas(element) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';
                
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = element.width;
                    canvas.height = element.height;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, element.width, element.height);
                    resolve(canvas);
                };
                
                img.onerror = () => {
                    // 使用GM_xmlhttpRequest作为备选方案
                    if (typeof GM_xmlhttpRequest !== 'undefined') {
                        GM_xmlhttpRequest({
                            method: 'GET',
                            url: element.url,
                            responseType: 'blob',
                            headers: {
                                'Referer': 'https://www.gaoding.com/'
                            },
                            onload: function(response) {
                                const blobUrl = URL.createObjectURL(response.response);
                                const fallbackImg = new Image();
                                fallbackImg.onload = () => {
                                    const canvas = document.createElement('canvas');
                                    canvas.width = element.width;
                                    canvas.height = element.height;
                                    const ctx = canvas.getContext('2d');
                                    ctx.drawImage(fallbackImg, 0, 0, element.width, element.height);
                                    URL.revokeObjectURL(blobUrl);
                                    resolve(canvas);
                                };
                                fallbackImg.onerror = () => {
                                    URL.revokeObjectURL(blobUrl);
                                    reject(new Error('图片加载失败'));
                                };
                                fallbackImg.src = blobUrl;
                            },
                            onerror: () => reject(new Error('网络请求失败'))
                        });
                    } else {
                        reject(new Error('图片加载失败且无备选方案'));
                    }
                };
                
                img.src = element.url;
            });
        }

        // 创建文本画布
        async createTextCanvas(element) {
            const canvas = document.createElement('canvas');
            canvas.width = element.width;
            canvas.height = element.height;
            const ctx = canvas.getContext('2d');

            // 设置字体
            const fontSize = element.fontSize || 16;
            const fontFamily = element.fontFamily || 'Arial, sans-serif';
            ctx.font = `${fontSize}px ${fontFamily}`;
            
            // 设置颜色
            ctx.fillStyle = element.color || '#000000';
            ctx.textAlign = element.textAlign || 'left';
            ctx.textBaseline = 'top';

            // 绘制文本
            const text = element.content || element.text || '';
            const lines = text.split('\n');
            const lineHeight = fontSize * 1.2;
            
            lines.forEach((line, index) => {
                ctx.fillText(line, 0, index * lineHeight);
            });

            return canvas;
        }

        // 创建形状画布
        async createShapeCanvas(element) {
            const canvas = document.createElement('canvas');
            canvas.width = element.width;
            canvas.height = element.height;
            const ctx = canvas.getContext('2d');

            ctx.fillStyle = element.fill || '#000000';
            
            if (element.shape === 'rectangle') {
                ctx.fillRect(0, 0, element.width, element.height);
            } else if (element.shape === 'circle') {
                ctx.beginPath();
                ctx.arc(element.width/2, element.height/2, Math.min(element.width, element.height)/2, 0, 2 * Math.PI);
                ctx.fill();
            }

            return canvas;
        }

        // 创建SVG画布
        async createSVGCanvas(element) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                const svgBlob = new Blob([element.svg || element.content], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);
                
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = element.width;
                    canvas.height = element.height;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, element.width, element.height);
                    URL.revokeObjectURL(url);
                    resolve(canvas);
                };
                
                img.onerror = () => {
                    URL.revokeObjectURL(url);
                    reject(new Error('SVG渲染失败'));
                };
                
                img.src = url;
            });
        }

        // 通用画布创建
        async createGenericCanvas(element) {
            // 尝试通过元素ID或类名在DOM中找到对应元素
            const selectors = [
                `[data-id="${element.id}"]`,
                `[id="${element.id}"]`,
                `.${element.type}-element`,
                `[data-type="${element.type}"]`
            ];

            for (const selector of selectors) {
                const domElement = document.querySelector(selector);
                if (domElement) {
                    try {
                        return await html2canvas(domElement, {
                            backgroundColor: null,
                            scale: 1,
                            useCORS: true,
                            allowTaint: true,
                            width: element.width,
                            height: element.height
                        });
                    } catch (e) {
                        console.warn(`html2canvas失败: ${selector}`, e);
                    }
                }
            }

            // 创建空白画布作为占位符
            const canvas = document.createElement('canvas');
            canvas.width = element.width;
            canvas.height = element.height;
            return canvas;
        }

        // 生成完整的PSD文件
        async generatePSD() {
            try {
                console.log('🎯 开始生成完整PSD文件...');

                // 获取设计数据
                this.designData = await this.extractDesignData();
                this.width = this.designData.width || 800;
                this.height = this.designData.height || 600;

                console.log(`📐 画布尺寸: ${this.width} x ${this.height}`);
                console.log(`🎨 发现 ${this.designData.elements.length} 个图层`);

                // 处理所有图层
                const canvasRect = { width: this.width, height: this.height };
                let processedCount = 0;

                for (let i = 0; i < this.designData.elements.length; i++) {
                    const element = this.designData.elements[i];
                    const success = await this.processLayer(element, i, canvasRect);
                    if (success) processedCount++;

                    // 显示进度
                    if (i % 5 === 0 || i === this.designData.elements.length - 1) {
                        console.log(`📊 进度: ${i + 1}/${this.designData.elements.length} (成功: ${processedCount})`);
                    }
                }

                if (this.layers.length === 0) {
                    throw new Error('没有成功处理任何图层');
                }

                // 构建PSD数据结构
                const psdData = {
                    width: this.width,
                    height: this.height,
                    channels: 4, // RGBA
                    bitsPerChannel: 8,
                    colorMode: 3, // RGB
                    children: []
                };

                // 添加所有图层到PSD
                for (const layer of this.layers) {
                    if (layer.canvas && layer.visible) {
                        psdData.children.push({
                            name: layer.name,
                            left: layer.left,
                            top: layer.top,
                            right: layer.left + layer.width,
                            bottom: layer.top + layer.height,
                            opacity: layer.opacity,
                            blendMode: 'normal',
                            canvas: layer.canvas
                        });
                    }
                }

                console.log(`✅ PSD数据构建完成，包含 ${psdData.children.length} 个图层`);

                // 生成PSD文件
                const psdBuffer = window.agPsd.writePsd(psdData);

                // 下载文件
                const blob = new Blob([psdBuffer], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `稿定设计_完整图层_${Date.now()}.psd`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                console.log('🎉 PSD文件导出成功！');
                alert(`✅ PSD导出成功！\n\n包含 ${psdData.children.length} 个独立图层\n文件大小: ${(blob.size / 1024 / 1024).toFixed(2)} MB`);

            } catch (error) {
                console.error('❌ PSD生成失败:', error);
                alert(`❌ PSD生成失败: ${error.message}`);
            }
        }
    }

    // 创建UI按钮
    function createExportButton() {
        const button = document.createElement('div');
        button.innerHTML = '📥 完整图层导出';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 10000;
            user-select: none;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.2);
        `;

        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
            button.style.boxShadow = '0 6px 20px rgba(0,0,0,0.3)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0)';
            button.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
        });

        button.addEventListener('click', async () => {
            if (isProcessing) {
                alert('⏳ 正在处理中，请稍候...');
                return;
            }

            isProcessing = true;
            button.innerHTML = '⏳ 导出中...';
            button.style.background = 'linear-gradient(135deg, #ffa726 0%, #fb8c00 100%)';

            try {
                const exporter = new CompleteLayerExporter();
                await exporter.generatePSD();
            } catch (error) {
                console.error('导出失败:', error);
                alert(`❌ 导出失败: ${error.message}`);
            } finally {
                isProcessing = false;
                button.innerHTML = '📥 完整图层导出';
                button.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            }
        });

        // 使按钮可拖拽
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        button.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // 左键
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                startLeft = parseInt(button.style.right) || 20;
                startTop = parseInt(button.style.top) || 20;
                button.style.cursor = 'grabbing';
                e.preventDefault();
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = startX - e.clientX;
                const deltaY = e.clientY - startY;
                button.style.right = (startLeft + deltaX) + 'px';
                button.style.top = (startTop + deltaY) + 'px';
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                button.style.cursor = 'pointer';
            }
        });

        document.body.appendChild(button);
        console.log('✅ 完整图层导出按钮已创建');
    }

    // 初始化
    async function init() {
        console.log('🚀 稿定设计完整图层导出工具启动');

        // 等待库加载
        await waitForLibraries();

        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createExportButton);
        } else {
            setTimeout(createExportButton, 1000);
        }
    }

    init();
})();
